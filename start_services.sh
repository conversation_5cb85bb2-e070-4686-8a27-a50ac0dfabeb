#!/bin/bash

# 启动服务脚本

echo "启动MultiAgentPPT服务..."

# 启动前端服务
echo "启动前端服务..."
cd frontend
npm run dev &
FRONTEND_PID=$!

# 等待前端启动
sleep 5

# 启动PPT导出服务
echo "启动PPT导出服务..."
cd ../backend/ppt_export
python3 main.py &
BACKEND_PID=$!

echo "服务启动完成！"
echo "前端服务: http://localhost:3000"
echo "PPT导出服务: http://localhost:8001"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '停止服务...'; kill $FRONTEND_PID $BACKEND_PID; exit" INT
wait
