# MultiAgentPPT 项目结构

## 新增的PPT导出功能文件

```
MultiAgentPPT/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   └── presentation/
│   │   │       └── presentation-page/
│   │   │           ├── buttons/
│   │   │           │   └── ExportButton.tsx          # 🆕 导出按钮组件
│   │   │           └── PresentationHeader.tsx        # ✏️ 已修改，添加导出按钮
│   │   └── app/
│   │       └── api/
│   │           └── presentation/
│   │               └── export/
│   │                   └── [id]/
│   │                       └── route.ts              # 🆕 导出API路由
│   └── package.json
├── backend/
│   ├── ppt_export/                                    # 🆕 PPT导出服务目录
│   │   ├── main.py                                    # 🆕 导出服务主程序
│   │   ├── requirements.txt                           # 🆕 Python依赖
│   │   ├── README.md                                  # 🆕 服务说明
│   │   └── start.sh                                   # 🆕 启动脚本
│   └── requirements.txt                               # ✏️ 已修改，添加python-pptx
├── start_services.sh                                  # 🆕 一键启动脚本
├── PPT导出功能说明.md                                  # 🆕 功能说明文档
└── 项目结构.md                                        # 🆕 项目结构说明
```

## 文件说明

### 🆕 新增文件

#### 前端文件
- **ExportButton.tsx**: 导出按钮组件，包含导出逻辑和UI
- **route.ts**: 导出API路由，处理前端到后端的导出请求

#### 后端文件
- **main.py**: PPT导出服务主程序，使用FastAPI和python-pptx
- **requirements.txt**: PPT导出服务的Python依赖
- **README.md**: PPT导出服务的详细说明
- **start.sh**: PPT导出服务启动脚本

#### 配置和文档
- **start_services.sh**: 一键启动前端和后端服务
- **PPT导出功能说明.md**: 完整的功能实现说明
- **项目结构.md**: 项目结构和文件说明

### ✏️ 修改文件

#### 前端文件
- **PresentationHeader.tsx**: 添加了导出按钮的导入和渲染

#### 后端文件
- **requirements.txt**: 添加了python-pptx、fastapi、uvicorn等依赖

## 核心功能模块

### 1. 前端导出模块
```
frontend/src/components/presentation/presentation-page/buttons/
└── ExportButton.tsx
    ├── 导出按钮UI
    ├── 加载状态管理
    ├── 错误处理
    └── 文件下载逻辑
```

### 2. API接口模块
```
frontend/src/app/api/presentation/export/[id]/
└── route.ts
    ├── 数据库查询
    ├── 数据格式转换
    ├── 后端服务调用
    └── 文件响应处理
```

### 3. 后端导出服务
```
backend/ppt_export/
├── main.py
│   ├── FastAPI应用
│   ├── PPTExporter类
│   ├── 幻灯片生成逻辑
│   └── 文件返回处理
├── requirements.txt
└── README.md
```

## 技术架构

### 前端技术栈
- **React**: 组件化UI开发
- **TypeScript**: 类型安全
- **Next.js**: 全栈框架
- **Tailwind CSS**: 样式框架
- **Lucide React**: 图标库

### 后端技术栈
- **Python**: 主要编程语言
- **FastAPI**: 高性能Web框架
- **python-pptx**: PowerPoint文件生成
- **uvicorn**: ASGI服务器

### 数据流
```
用户点击导出按钮
    ↓
ExportButton.tsx
    ↓
/api/presentation/export/[id]
    ↓
数据库查询演示文稿数据
    ↓
调用PPT导出服务
    ↓
python-pptx生成PPT文件
    ↓
返回文件给用户下载
```

## 部署说明

### 开发环境
1. 启动前端: `cd frontend && npm run dev`
2. 启动后端: `cd backend/ppt_export && python3 main.py`
3. 或使用一键启动: `./start_services.sh`

### 生产环境
1. 前端构建: `cd frontend && npm run build`
2. 后端部署: 使用Docker或直接部署Python服务
3. 配置反向代理: Nginx配置前后端路由

## 扩展性

### 功能扩展点
- 支持更多PPT元素类型
- 添加主题和模板支持
- 实现批量导出功能
- 添加导出进度显示

### 架构扩展点
- 微服务化部署
- 消息队列异步处理
- 缓存层优化
- 监控和日志系统
