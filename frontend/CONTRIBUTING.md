# Contributing to ALLWEONE Presentation Generator

Thank you for your interest in contributing to the ALLWEONE Presentation Generator! This document provides guidelines and instructions for contributing to this project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and considerate of others.

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork**
   ```bash
   <NAME_EMAIL>:allweonedev/presentation-ai.git
   cd presentation-ai
   ```
3. **Set up the development environment**
   ```bash
   pnpm install
   ```
4. **Create a new branch** for your contribution
   ```bash
   git checkout -b feature/your-feature-name
   ```

## Development Workflow

1. Make your changes locally
2. Follow the code style guidelines
3. Write tests for your changes when applicable
4. Ensure all tests pass with `pnpm test`
5. Commit your changes using clear commit messages
6. Push your branch to your fork
7. Open a pull request from your fork to the main repository

## Pull Request Process

1. **Use a clear and descriptive title** for your PR
2. **Include a detailed description** explaining what your changes do and why they should be included
3. **Link any related issues** using GitHub issue references
4. **Update documentation** if your changes modify functionality
5. **Ensure CI passes** on your PR
6. Wait for a maintainer to review your PR
7. Address any feedback from reviewers
8. Once approved, a maintainer will merge your PR

## Coding Standards

- Follow the existing code style in the project
- Use meaningful variable and function names
- Write clear comments for complex logic
- Keep functions focused on a single responsibility
- Follow TypeScript best practices and maintain type safety

## Commit Guidelines

We follow conventional commit messages:

- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding or modifying tests
- `chore:` for changes to the build process or auxiliary tools

## Documentation

- Update documentation when adding or changing features
- Use clear, concise language
- Include examples when helpful

## Issue Reporting

If you find a bug or have a feature request:

1. Check if it already exists in the [Issues](https://github.com/allweonedev/presentation-ai/issues)
2. If not, create a new issue using the appropriate template
3. Provide detailed steps to reproduce bugs
4. For feature requests, explain the use case and benefits

## Questions or Need Help?

If you have questions or need help, you can:

- Open a discussion on GitHub
- Reach out via the project's communication channels

## License

By contributing to this project, you agree that your contributions will be licensed under the project's [MIT License](LICENSE).

Thank you for contributing to make ALLWEONE Presentation Generator better for everyone!
