lockfileVersion: 5.3

specifiers:
  '@a2a-js/sdk': ^0.2.2
  '@ai-sdk/openai': ^1.1.5
  '@ai-sdk/react': ^0.0.6
  '@ariakit/react': ^0.4.15
  '@dnd-kit/core': ^6.3.1
  '@dnd-kit/sortable': ^10.0.0
  '@dnd-kit/utilities': ^3.2.2
  '@fortawesome/fontawesome-svg-core': ^6.7.2
  '@fortawesome/free-brands-svg-icons': ^6.7.2
  '@fortawesome/free-regular-svg-icons': ^6.7.2
  '@fortawesome/free-solid-svg-icons': ^6.7.2
  '@fortawesome/react-fontawesome': ^0.2.2
  '@hookform/resolvers': ^3.10.0
  '@langchain/core': 0.3.36
  '@langchain/openai': ^0.4.2
  '@prisma/client': ^5.22.0
  '@radix-ui/react-accordion': ^1.2.2
  '@radix-ui/react-alert-dialog': ^1.1.5
  '@radix-ui/react-aspect-ratio': ^1.1.1
  '@radix-ui/react-avatar': ^1.1.2
  '@radix-ui/react-checkbox': ^1.1.3
  '@radix-ui/react-collapsible': ^1.1.2
  '@radix-ui/react-context-menu': ^2.2.5
  '@radix-ui/react-dialog': ^1.1.5
  '@radix-ui/react-dropdown-menu': ^2.1.5
  '@radix-ui/react-hover-card': ^1.1.5
  '@radix-ui/react-label': ^2.1.1
  '@radix-ui/react-menubar': ^1.1.5
  '@radix-ui/react-navigation-menu': ^1.2.4
  '@radix-ui/react-popover': ^1.1.5
  '@radix-ui/react-progress': ^1.1.1
  '@radix-ui/react-radio-group': ^1.2.2
  '@radix-ui/react-scroll-area': ^1.2.2
  '@radix-ui/react-select': ^2.1.5
  '@radix-ui/react-separator': ^1.1.1
  '@radix-ui/react-slider': ^1.2.2
  '@radix-ui/react-slot': ^1.1.1
  '@radix-ui/react-switch': ^1.1.2
  '@radix-ui/react-tabs': ^1.1.2
  '@radix-ui/react-toast': ^1.2.5
  '@radix-ui/react-toggle': ^1.1.1
  '@radix-ui/react-toggle-group': ^1.1.1
  '@radix-ui/react-toolbar': ^1.1.1
  '@radix-ui/react-tooltip': ^1.1.7
  '@t3-oss/env-nextjs': ^0.10.1
  '@tailwindcss/container-queries': ^0.1.1
  '@tailwindcss/typography': ^0.5.16
  '@tanstack/react-query': ^5.65.1
  '@types/eslint': ^8.56.12
  '@types/lodash.debounce': ^4.0.9
  '@types/node': ^20.17.16
  '@types/prismjs': ^1.26.5
  '@types/react': ^18.3.18
  '@types/react-dom': ^18.3.5
  '@types/webpack': ^5.28.5
  '@typescript-eslint/eslint-plugin': ^7.18.0
  '@typescript-eslint/parser': ^7.18.0
  '@udecode/cmdk': ^0.1.1
  '@udecode/cn': ^40.2.8
  '@udecode/plate': ^41.0.14
  '@udecode/plate-ai': ^41.0.14
  '@udecode/plate-alignment': ^41.0.0
  '@udecode/plate-autoformat': ^41.0.0
  '@udecode/plate-basic-elements': ^41.0.0
  '@udecode/plate-basic-marks': ^41.0.0
  '@udecode/plate-block-quote': ^41.0.0
  '@udecode/plate-break': ^41.0.0
  '@udecode/plate-callout': ^41.0.0
  '@udecode/plate-caption': ^41.0.0
  '@udecode/plate-code-block': ^41.0.0
  '@udecode/plate-combobox': ^41.0.0
  '@udecode/plate-comments': ^41.0.0
  '@udecode/plate-common': ^41.0.13
  '@udecode/plate-core': ^41.0.13
  '@udecode/plate-date': ^41.0.0
  '@udecode/plate-dnd': ^41.0.2
  '@udecode/plate-emoji': ^41.0.0
  '@udecode/plate-excalidraw': ^41.0.0
  '@udecode/plate-floating': ^41.0.0
  '@udecode/plate-font': ^41.0.12
  '@udecode/plate-heading': ^41.0.0
  '@udecode/plate-highlight': ^41.0.0
  '@udecode/plate-horizontal-rule': ^41.0.0
  '@udecode/plate-indent': ^41.0.0
  '@udecode/plate-indent-list': ^41.0.10
  '@udecode/plate-juice': ^41.0.0
  '@udecode/plate-kbd': ^41.0.0
  '@udecode/plate-layout': ^41.0.2
  '@udecode/plate-line-height': ^41.0.0
  '@udecode/plate-link': ^41.0.0
  '@udecode/plate-list': ^41.0.0
  '@udecode/plate-markdown': ^41.0.14
  '@udecode/plate-math': ^41.0.11
  '@udecode/plate-media': ^41.0.0
  '@udecode/plate-mention': ^41.0.0
  '@udecode/plate-node-id': ^41.0.0
  '@udecode/plate-reset-node': ^41.0.0
  '@udecode/plate-resizable': ^41.0.0
  '@udecode/plate-select': ^41.0.0
  '@udecode/plate-selection': ^41.0.8
  '@udecode/plate-slash-command': ^41.0.0
  '@udecode/plate-tabbable': ^41.0.0
  '@udecode/plate-table': ^41.0.9
  '@udecode/plate-toggle': ^41.0.0
  '@udecode/plate-trailing-block': ^41.0.0
  '@uploadthing/react': ^7.1.5
  ai: ^4.1.10
  class-variance-authority: ^0.7.1
  clsx: ^2.1.1
  cmdk: ^1.0.4
  date-fns: ^3.6.0
  embla-carousel-react: ^8.5.2
  eslint: ^8.57.1
  eslint-config-next: ^14.2.23
  input-otp: ^1.4.2
  langchain: ^0.3.14
  lodash.debounce: ^4.0.8
  lucide-react: ^0.379.0
  nanoid: ^5.0.9
  next: 14.2.23
  next-themes: ^0.3.0
  postcss: ^8.5.1
  prettier: ^3.4.2
  prettier-plugin-tailwindcss: ^0.5.14
  prisma: ^5.22.0
  prismjs: ^1.29.0
  prosemirror-commands: ^1.6.2
  prosemirror-history: ^1.4.1
  prosemirror-keymap: ^1.2.2
  prosemirror-markdown: ^1.13.1
  prosemirror-model: ^1.24.1
  prosemirror-schema-basic: ^1.2.3
  prosemirror-schema-list: ^1.5.0
  prosemirror-state: ^1.4.3
  prosemirror-view: ^1.38.0
  re-resizable: ^6.10.3
  react: 18.2.0
  react-colorful: ^5.6.1
  react-day-picker: ^8.10.1
  react-dnd: ^16.0.1
  react-dnd-html5-backend: ^16.0.1
  react-dom: 18.2.0
  react-dropzone: ^14.3.5
  react-file-picker: ^0.0.6
  react-fontpicker-ts: ^1.2.0
  react-hook-form: ^7.54.2
  react-icons: ^5.5.0
  react-icons-picker: ^1.0.9
  react-intersection-observer: ^9.15.1
  react-resizable-panels: ^2.1.7
  recharts: ^2.15.1
  slate: ^0.103.0
  slate-history: ^0.113.1
  slate-hyperscript: ^0.100.0
  slate-react: ^0.110.3
  sonner: ^1.7.2
  tailwind-merge: ^2.6.0
  tailwind-scrollbar-hide: ^2.0.0
  tailwindcss: ^3.4.17
  tailwindcss-animate: ^1.0.7
  tailwindcss-scrollbar: ^0.1.0
  together-ai: ^0.7.0
  ts-node: ^10.9.2
  typescript: ^5.7.3
  uploadthing: ^7.4.4
  use-file-picker: ^2.1.2
  vaul: ^0.9.9
  webpack: ^5.97.1
  zod: ^3.24.1
  zustand: ^4.5.6

dependencies:
  '@a2a-js/sdk': 0.2.2
  '@ai-sdk/openai': 1.3.22_zod@3.25.70
  '@ai-sdk/react': 0.0.6_react@18.2.0+zod@3.25.70
  '@ariakit/react': 0.4.17_react-dom@18.2.0+react@18.2.0
  '@dnd-kit/core': 6.3.1_react-dom@18.2.0+react@18.2.0
  '@dnd-kit/sortable': 10.0.0_@dnd-kit+core@6.3.1+react@18.2.0
  '@dnd-kit/utilities': 3.2.2_react@18.2.0
  '@fortawesome/fontawesome-svg-core': 6.7.2
  '@fortawesome/free-brands-svg-icons': 6.7.2
  '@fortawesome/free-regular-svg-icons': 6.7.2
  '@fortawesome/free-solid-svg-icons': 6.7.2
  '@fortawesome/react-fontawesome': 0.2.2_65b02046fa8c2f0654197e24dbca04ed
  '@hookform/resolvers': 3.10.0_react-hook-form@7.59.0
  '@langchain/core': 0.3.36
  '@langchain/openai': 0.4.9_@langchain+core@0.3.36
  '@prisma/client': 5.22.0_prisma@5.22.0
  '@radix-ui/react-accordion': 1.2.11_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-alert-dialog': 1.1.14_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-aspect-ratio': 1.1.7_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-avatar': 1.1.10_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-checkbox': 1.3.2_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-collapsible': 1.1.11_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-context-menu': 2.2.15_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-dialog': 1.1.14_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-dropdown-menu': 2.1.15_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-hover-card': 1.1.14_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-label': 2.1.7_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-menubar': 1.1.15_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-navigation-menu': 1.2.13_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-popover': 1.1.14_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-progress': 1.1.7_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-radio-group': 1.3.7_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-scroll-area': 1.2.9_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-select': 2.2.5_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-separator': 1.1.7_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-slider': 1.3.5_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
  '@radix-ui/react-switch': 1.2.5_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-tabs': 1.1.12_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-toast': 1.2.14_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-toggle': 1.1.9_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-toggle-group': 1.1.10_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-toolbar': 1.1.10_813bbe29c70008681efc49db0ea78d97
  '@radix-ui/react-tooltip': 1.2.7_813bbe29c70008681efc49db0ea78d97
  '@t3-oss/env-nextjs': 0.10.1_typescript@5.8.3+zod@3.25.70
  '@tanstack/react-query': 5.81.5_react@18.2.0
  '@types/lodash.debounce': 4.0.9
  '@types/prismjs': 1.26.5
  '@udecode/cmdk': 0.1.1_813bbe29c70008681efc49db0ea78d97
  '@udecode/cn': 40.2.8_db7f2e5930f556bc9133bdfa61b2ae1e
  '@udecode/plate': 41.0.14_5375e8c3125b784b45e0c8f07e4fcfda
  '@udecode/plate-ai': 41.0.14_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-alignment': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-autoformat': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-basic-elements': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-basic-marks': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-block-quote': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-break': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-callout': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-caption': 41.0.0_7a26c35e4a848caccaecf24742ce5138
  '@udecode/plate-code-block': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-combobox': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-comments': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
  '@udecode/plate-core': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
  '@udecode/plate-date': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-dnd': 41.0.2_68ca63d7af8cbe90af517060dbbd08a6
  '@udecode/plate-emoji': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-excalidraw': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-floating': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-font': 41.0.12_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-heading': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-highlight': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-horizontal-rule': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-indent': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-indent-list': 41.0.10_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-juice': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-kbd': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-layout': 41.0.2_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-line-height': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-link': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-list': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-markdown': 41.0.14_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-math': 41.0.11_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-media': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-mention': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-node-id': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-reset-node': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-resizable': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-select': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-selection': 41.0.8_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-slash-command': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-tabbable': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-table': 41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-toggle': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@udecode/plate-trailing-block': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
  '@uploadthing/react': 7.3.2_95e3019cd4c89d97ff3e5bf49022e184
  ai: 4.3.16_react@18.2.0+zod@3.25.70
  class-variance-authority: 0.7.1
  clsx: 2.1.1
  cmdk: 1.1.1_813bbe29c70008681efc49db0ea78d97
  date-fns: 3.6.0
  embla-carousel-react: 8.6.0_react@18.2.0
  input-otp: 1.4.2_react-dom@18.2.0+react@18.2.0
  langchain: 0.3.29_@langchain+core@0.3.36
  lodash.debounce: 4.0.8
  lucide-react: 0.379.0_react@18.2.0
  nanoid: 5.1.5
  next: 14.2.23_react-dom@18.2.0+react@18.2.0
  next-themes: 0.3.0_react-dom@18.2.0+react@18.2.0
  prisma: 5.22.0
  prismjs: 1.30.0
  prosemirror-commands: 1.7.1
  prosemirror-history: 1.4.1
  prosemirror-keymap: 1.2.3
  prosemirror-markdown: 1.13.2
  prosemirror-model: 1.25.1
  prosemirror-schema-basic: 1.2.4
  prosemirror-schema-list: 1.5.1
  prosemirror-state: 1.4.3
  prosemirror-view: 1.40.0
  re-resizable: 6.11.2_react-dom@18.2.0+react@18.2.0
  react: 18.2.0
  react-colorful: 5.6.1_react-dom@18.2.0+react@18.2.0
  react-day-picker: 8.10.1_date-fns@3.6.0+react@18.2.0
  react-dnd: 16.0.1_fef9396d7cc8c8d91fc915a58da7a30e
  react-dnd-html5-backend: 16.0.1
  react-dom: 18.2.0_react@18.2.0
  react-dropzone: 14.3.8_react@18.2.0
  react-file-picker: 0.0.6
  react-fontpicker-ts: 1.2.0_react@18.2.0
  react-hook-form: 7.59.0_react@18.2.0
  react-icons: 5.5.0_react@18.2.0
  react-icons-picker: 1.0.9_dd472908a3e86255eec0e40e6bef4372
  react-intersection-observer: 9.16.0_react-dom@18.2.0+react@18.2.0
  react-resizable-panels: 2.1.9_react-dom@18.2.0+react@18.2.0
  recharts: 2.15.4_react-dom@18.2.0+react@18.2.0
  slate: 0.103.0
  slate-history: 0.113.1_slate@0.103.0
  slate-hyperscript: 0.100.0_slate@0.103.0
  slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
  sonner: 1.7.4_react-dom@18.2.0+react@18.2.0
  tailwind-merge: 2.6.0
  together-ai: 0.7.0
  uploadthing: 7.7.3_next@14.2.23+tailwindcss@3.4.17
  use-file-picker: 2.1.4_7060a42dad9f445ac5f4d9021dc0257f
  vaul: 0.9.9_813bbe29c70008681efc49db0ea78d97
  zod: 3.25.70
  zustand: 4.5.7_888dbdc24f3e5b913723df3d4309886e

devDependencies:
  '@tailwindcss/container-queries': 0.1.1_tailwindcss@3.4.17
  '@tailwindcss/typography': 0.5.16_tailwindcss@3.4.17
  '@types/eslint': 8.56.12
  '@types/node': 20.19.4
  '@types/react': 18.3.23
  '@types/react-dom': 18.3.7_@types+react@18.3.23
  '@types/webpack': 5.28.5
  '@typescript-eslint/eslint-plugin': 7.18.0_ec52852665279724a7bf6969b667668f
  '@typescript-eslint/parser': 7.18.0_eslint@8.57.1+typescript@5.8.3
  eslint: 8.57.1
  eslint-config-next: 14.2.30_eslint@8.57.1+typescript@5.8.3
  postcss: 8.5.6
  prettier: 3.6.2
  prettier-plugin-tailwindcss: 0.5.14_prettier@3.6.2
  tailwind-scrollbar-hide: 2.0.0_tailwindcss@3.4.17
  tailwindcss: 3.4.17_ts-node@10.9.2
  tailwindcss-animate: 1.0.7_tailwindcss@3.4.17
  tailwindcss-scrollbar: 0.1.0_tailwindcss@3.4.17
  ts-node: 10.9.2_41655fe179e000f935c12012a5853c75
  typescript: 5.8.3
  webpack: 5.99.9

packages:

  /@a2a-js/sdk/0.2.2:
    resolution: {integrity: sha512-T0iFXgU1Z7MQWmFKLALaAnfWCvwDm8TVuCXolYymOHBh7lEawM30b06wOLc/u4wOWujuGQyCEtdVVFFF80Azbw==}
    engines: {node: '>=18'}
    dependencies:
      '@types/cors': 2.8.19
      '@types/express': 5.0.3
      body-parser: 2.2.0
      cors: 2.8.5
      express: 4.21.2
      uuid: 11.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@ai-sdk/openai/1.3.22_zod@3.25.70:
    resolution: {integrity: sha512-QwA+2EkG0QyjVR+7h6FE7iOu2ivNqAVMm9UJZkVxxTk5OIq5fFJDTEI/zICEMuHImTTXR2JjsL6EirJ28Jc4cw==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.0.0
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8_zod@3.25.70
      zod: 3.25.70
    dev: false

  /@ai-sdk/provider-utils/0.0.16_zod@3.25.70:
    resolution: {integrity: sha512-W2zUZ+C5uDr2P9/KZwtV4r4F0l2RlD0AvtJyug7ER5g3hGHAfKrPM0y2hSlRxNfph5BTCC6YQX0nFLyBph+6bQ==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.0.0
    peerDependenciesMeta:
      zod:
        optional: true
    dependencies:
      '@ai-sdk/provider': 0.0.10
      eventsource-parser: 1.1.2
      nanoid: 3.3.6
      secure-json-parse: 2.7.0
      zod: 3.25.70
    dev: false

  /@ai-sdk/provider-utils/2.2.8_zod@3.25.70:
    resolution: {integrity: sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.23.8
    dependencies:
      '@ai-sdk/provider': 1.1.3
      nanoid: 3.3.11
      secure-json-parse: 2.7.0
      zod: 3.25.70
    dev: false

  /@ai-sdk/provider/0.0.10:
    resolution: {integrity: sha512-NzkrtREQpHID1cTqY/C4CI30PVOaXWKYytDR2EcytmFgnP7Z6+CrGIA/YCnNhYAuUm6Nx+nGpRL/Hmyrv7NYzg==}
    engines: {node: '>=18'}
    dependencies:
      json-schema: 0.4.0
    dev: false

  /@ai-sdk/provider/1.1.3:
    resolution: {integrity: sha512-qZMxYJ0qqX/RfnuIaab+zp8UAeJn/ygXXAffR5I4N0n1IrvA6qBsjc8hXLmBiMV2zoXlifkacF7sEFnYnjBcqg==}
    engines: {node: '>=18'}
    dependencies:
      json-schema: 0.4.0
    dev: false

  /@ai-sdk/react/0.0.6_react@18.2.0+zod@3.25.70:
    resolution: {integrity: sha512-k0gpsiUxDTkDMYsdWl0WhujXVPMZvfPMCSbxZtzGvEao0YZNFQ/9ct+SZJvb5t9126oiNG0a344COO9CZ1OdLg==}
    engines: {node: '>=18'}
    peerDependencies:
      react: ^18 || ^19
      zod: ^3.0.0
    peerDependenciesMeta:
      react:
        optional: true
      zod:
        optional: true
    dependencies:
      '@ai-sdk/provider-utils': 0.0.16_zod@3.25.70
      '@ai-sdk/ui-utils': 0.0.5_zod@3.25.70
      react: 18.2.0
      swr: 2.2.0_react@18.2.0
      zod: 3.25.70
    dev: false

  /@ai-sdk/react/1.2.12_react@18.2.0+zod@3.25.70:
    resolution: {integrity: sha512-jK1IZZ22evPZoQW3vlkZ7wvjYGYF+tRBKXtrcolduIkQ/m/sOAVcVeVDUDvh1T91xCnWCdUGCPZg2avZ90mv3g==}
    engines: {node: '>=18'}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      zod: ^3.23.8
    peerDependenciesMeta:
      zod:
        optional: true
    dependencies:
      '@ai-sdk/provider-utils': 2.2.8_zod@3.25.70
      '@ai-sdk/ui-utils': 1.2.11_zod@3.25.70
      react: 18.2.0
      swr: 2.3.4_react@18.2.0
      throttleit: 2.1.0
      zod: 3.25.70
    dev: false

  /@ai-sdk/ui-utils/0.0.5_zod@3.25.70:
    resolution: {integrity: sha512-Ug2qsKVLLxzZtJMu8Omw7wA1p8RqX82M4OeAZ2/oCPlZSAVAte+VnuXl6q6lUsAUfprVCDpzDDm9GJOOOYZg2Q==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.0.0
    peerDependenciesMeta:
      zod:
        optional: true
    dependencies:
      '@ai-sdk/provider-utils': 0.0.16_zod@3.25.70
      secure-json-parse: 2.7.0
      zod: 3.25.70
    dev: false

  /@ai-sdk/ui-utils/1.2.11_zod@3.25.70:
    resolution: {integrity: sha512-3zcwCc8ezzFlwp3ZD15wAPjf2Au4s3vAbKsXQVyhxODHcmu0iyPO2Eua6D/vicq/AUm/BAo60r97O6HU+EI0+w==}
    engines: {node: '>=18'}
    peerDependencies:
      zod: ^3.23.8
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8_zod@3.25.70
      zod: 3.25.70
      zod-to-json-schema: 3.24.6_zod@3.25.70
    dev: false

  /@alloc/quick-lru/5.2.0:
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}
    dev: true

  /@ariakit/core/0.4.15:
    resolution: {integrity: sha512-vvxmZvkNhiisKM+Y1TbGMUfVVchV/sWu9F0xw0RYADXcimWPK31dd9JnIZs/OQ5pwAryAHmERHwuGQVESkSjwQ==}
    dev: false

  /@ariakit/react-core/0.4.17_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-kFF6n+gC/5CRQIyaMTFoBPio2xUe0k9rZhMNdUobWRmc/twfeLVkODx+8UVYaNyKilTge8G0JFqwvFKku/jKEw==}
    peerDependencies:
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      '@ariakit/core': 0.4.15
      '@floating-ui/dom': 1.7.2
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      use-sync-external-store: 1.5.0_react@18.2.0
    dev: false

  /@ariakit/react/0.4.17_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-HQaIboE2axtlncJz1hRTaiQfJ1GGjhdtNcAnPwdjvl2RybfmlHowIB+HTVBp36LzroKPs/M4hPCxk7XTaqRZGg==}
    peerDependencies:
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      '@ariakit/react-core': 0.4.17_react-dom@18.2.0+react@18.2.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@babel/runtime/7.27.6:
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@cfworker/json-schema/4.1.1:
    resolution: {integrity: sha512-gAmrUZSGtKc3AiBL71iNWxDsyUC5uMaKKGdvzYsBoTW/xi42JQHl7eKV2OYzCUqvc+D2RCcf7EXY2iCyFIk6og==}
    dev: false

  /@cspotcode/source-map-support/0.8.1:
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==}
    engines: {node: '>=12'}
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9
    dev: true

  /@dnd-kit/accessibility/3.1.1_react@18.2.0:
    resolution: {integrity: sha512-2P+YgaXF+gRsIihwwY1gCsQSYnu9Zyj2py8kY5fFvUM1qm2WA2u639R6YNVfU4GWr+ZM5mqEsfHZZLoRONbemw==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
      tslib: 2.8.1
    dev: false

  /@dnd-kit/core/6.3.1_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-xkGBRQQab4RLwgXxoqETICr6S5JlogafbhNsidmrkVv2YRs5MLwpjoF2qpiGjQt8S9AoxtIV603s0GIUpY5eYQ==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@dnd-kit/accessibility': 3.1.1_react@18.2.0
      '@dnd-kit/utilities': 3.2.2_react@18.2.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      tslib: 2.8.1
    dev: false

  /@dnd-kit/sortable/10.0.0_@dnd-kit+core@6.3.1+react@18.2.0:
    resolution: {integrity: sha512-+xqhmIIzvAYMGfBYYnbKuNicfSsk4RksY2XdmJhT+HAC01nix6fHCztU68jooFiMUB01Ky3F0FyOvhG/BZrWkg==}
    peerDependencies:
      '@dnd-kit/core': ^6.3.0
      react: '>=16.8.0'
    dependencies:
      '@dnd-kit/core': 6.3.1_react-dom@18.2.0+react@18.2.0
      '@dnd-kit/utilities': 3.2.2_react@18.2.0
      react: 18.2.0
      tslib: 2.8.1
    dev: false

  /@dnd-kit/utilities/3.2.2_react@18.2.0:
    resolution: {integrity: sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
      tslib: 2.8.1
    dev: false

  /@effect/platform/0.85.2_effect@3.16.8:
    resolution: {integrity: sha512-zIRixbQeO6QniR0k2mwR7DmR2HO1w6+qQlzQ5nb8lyPyPgd1gV9wo/9yBeB6zRC+CGnxiUiYsRMamclVISuxLw==}
    peerDependencies:
      effect: ^3.16.8
    dependencies:
      effect: 3.16.8
      find-my-way-ts: 0.1.5
      msgpackr: 1.11.4
      multipasta: 0.2.5
    dev: false

  /@emnapi/core/1.4.3:
    resolution: {integrity: sha512-4m62DuCE07lw01soJwPiBGC0nAww0Q+RY70VZ+n49yDIO13yyinhbWCeNnaob0lakDtWQzSdtNWzJeOJt2ma+g==}
    dependencies:
      '@emnapi/wasi-threads': 1.0.2
      tslib: 2.8.1
    dev: true
    optional: true

  /@emnapi/runtime/1.4.3:
    resolution: {integrity: sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==}
    dependencies:
      tslib: 2.8.1
    dev: true
    optional: true

  /@emnapi/wasi-threads/1.0.2:
    resolution: {integrity: sha512-5n3nTJblwRi8LlXkJ9eBzu+kZR8Yxcc7ubakyQTFzPMtIhFpUBRbsnc2Dv88IZDIbCDlBiWrknhB4Lsz7mg6BA==}
    dependencies:
      tslib: 2.8.1
    dev: true
    optional: true

  /@emoji-mart/data/1.2.1:
    resolution: {integrity: sha512-no2pQMWiBy6gpBEiqGeU77/bFejDqUTRY7KX+0+iur13op3bqUsXdnwoZs6Xb1zbv0gAj5VvS1PWoUUckSr5Dw==}
    dev: false

  /@eslint-community/eslint-utils/4.7.0_eslint@8.57.1:
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: 8.57.1
      eslint-visitor-keys: 3.4.3
    dev: true

  /@eslint-community/regexpp/4.12.1:
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  /@eslint/eslintrc/2.1.4:
    resolution: {integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@eslint/js/8.57.1:
    resolution: {integrity: sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /@excalidraw/excalidraw/0.16.4_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-x56YTb5jmHAJ9SP2R81ywU28Y+QlOgjmCYHVMgHKPhh1hwKzimt+Z+iz/Rf2x1JpQOJRYbfeoxiGPQNhnYwGWQ==}
    peerDependencies:
      react: ^17.0.2 || ^18.2.0
      react-dom: ^17.0.2 || ^18.2.0
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@floating-ui/core/1.7.2:
    resolution: {integrity: sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==}
    dependencies:
      '@floating-ui/utils': 0.2.10
    dev: false

  /@floating-ui/dom/1.7.2:
    resolution: {integrity: sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==}
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/utils': 0.2.10
    dev: false

  /@floating-ui/react-dom/2.1.4_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-JbbpPhp38UmXDDAu60RJmbeme37Jbgsm7NrHGgzYYFKmblzRUh6Pa641dII6LsjwF4XlScDrde2UAzDo/b9KPw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/dom': 1.7.2
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@floating-ui/react/0.26.28_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-yORQuuAtVpiRjpMhdc0wJj06b9JFjrYF4qp96j++v2NBpbi6SEGF7donUJ3TMieerQ6qVkAv1tgr7L4r5roTqw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@floating-ui/react-dom': 2.1.4_react-dom@18.2.0+react@18.2.0
      '@floating-ui/utils': 0.2.10
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      tabbable: 6.2.0
    dev: false

  /@floating-ui/utils/0.2.10:
    resolution: {integrity: sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==}
    dev: false

  /@fortawesome/fontawesome-common-types/6.7.2:
    resolution: {integrity: sha512-Zs+YeHUC5fkt7Mg1l6XTniei3k4bwG/yo3iFUtZWd/pMx9g3fdvkSK9E0FOC+++phXOka78uJcYb8JaFkW52Xg==}
    engines: {node: '>=6'}
    dev: false

  /@fortawesome/fontawesome-svg-core/6.7.2:
    resolution: {integrity: sha512-yxtOBWDrdi5DD5o1pmVdq3WMCvnobT0LU6R8RyyVXPvFRd2o79/0NCuQoCjNTeZz9EzA9xS3JxNWfv54RIHFEA==}
    engines: {node: '>=6'}
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2
    dev: false

  /@fortawesome/free-brands-svg-icons/6.7.2:
    resolution: {integrity: sha512-zu0evbcRTgjKfrr77/2XX+bU+kuGfjm0LbajJHVIgBWNIDzrhpRxiCPNT8DW5AdmSsq7Mcf9D1bH0aSeSUSM+Q==}
    engines: {node: '>=6'}
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2
    dev: false

  /@fortawesome/free-regular-svg-icons/6.7.2:
    resolution: {integrity: sha512-7Z/ur0gvCMW8G93dXIQOkQqHo2M5HLhYrRVC0//fakJXxcF1VmMPsxnG6Ee8qEylA8b8Q3peQXWMNZ62lYF28g==}
    engines: {node: '>=6'}
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2
    dev: false

  /@fortawesome/free-solid-svg-icons/6.7.2:
    resolution: {integrity: sha512-GsBrnOzU8uj0LECDfD5zomZJIjrPhIlWU82AHwa2s40FKH+kcxQaBvBo3Z4TxyZHIyX8XTDxsyA33/Vx9eFuQA==}
    engines: {node: '>=6'}
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2
    dev: false

  /@fortawesome/react-fontawesome/0.2.2_65b02046fa8c2f0654197e24dbca04ed:
    resolution: {integrity: sha512-EnkrprPNqI6SXJl//m29hpaNzOp1bruISWaOiRtkMi/xSvHJlzc2j2JAYS7egxt/EbjSNV/k6Xy0AQI6vB2+1g==}
    peerDependencies:
      '@fortawesome/fontawesome-svg-core': ~1 || ~6
      react: '>=16.3'
    dependencies:
      '@fortawesome/fontawesome-svg-core': 6.7.2
      prop-types: 15.8.1
      react: 18.2.0
    dev: false

  /@hookform/resolvers/3.10.0_react-hook-form@7.59.0:
    resolution: {integrity: sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==}
    peerDependencies:
      react-hook-form: ^7.0.0
    dependencies:
      react-hook-form: 7.59.0_react@18.2.0
    dev: false

  /@humanwhocodes/config-array/0.13.0:
    resolution: {integrity: sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead
    dependencies:
      '@humanwhocodes/object-schema': 2.0.3
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@humanwhocodes/module-importer/1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}
    dev: true

  /@humanwhocodes/object-schema/2.0.3:
    resolution: {integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==}
    deprecated: Use @eslint/object-schema instead
    dev: true

  /@isaacs/cliui/8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width/4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: /strip-ansi/6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi/7.0.0
    dev: true

  /@jridgewell/gen-mapping/0.3.12:
    resolution: {integrity: sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==}
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29
    dev: true

  /@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/source-map/0.3.10:
    resolution: {integrity: sha512-0pPkgz9dY+bijgistcTTJ5mR+ocqRXLuhXHYdzoMmmoJ2C9S46RCm2GMUbatPEUK9Yjy26IrAy8D/M00lLkv+Q==}
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
    dev: true

  /@jridgewell/sourcemap-codec/1.5.4:
    resolution: {integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==}
    dev: true

  /@jridgewell/trace-mapping/0.3.29:
    resolution: {integrity: sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4
    dev: true

  /@jridgewell/trace-mapping/0.3.9:
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4
    dev: true

  /@juggle/resize-observer/3.4.0:
    resolution: {integrity: sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA==}
    dev: false

  /@langchain/core/0.3.36:
    resolution: {integrity: sha512-lOS6f5o2MarjGPomHPhzde9xI3lZW2NIOEdCv0dvjb1ZChWhwXWHtAMHSZmuSB53ySzDWAMkimimHd+Yqz5MwQ==}
    engines: {node: '>=18'}
    dependencies:
      '@cfworker/json-schema': 4.1.1
      ansi-styles: 5.2.0
      camelcase: 6.3.0
      decamelize: 1.2.0
      js-tiktoken: 1.0.20
      langsmith: 0.3.37
      mustache: 4.2.0
      p-queue: 6.6.2
      p-retry: 4.6.2
      uuid: 10.0.0
      zod: 3.25.70
      zod-to-json-schema: 3.24.6_zod@3.25.70
    transitivePeerDependencies:
      - '@opentelemetry/api'
      - '@opentelemetry/exporter-trace-otlp-proto'
      - '@opentelemetry/sdk-trace-base'
      - openai
    dev: false

  /@langchain/openai/0.4.9_@langchain+core@0.3.36:
    resolution: {integrity: sha512-NAsaionRHNdqaMjVLPkFCyjUDze+OqRHghA1Cn4fPoAafz+FXcl9c7LlEl9Xo0FH6/8yiCl7Rw2t780C/SBVxQ==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.3.39 <0.4.0'
    dependencies:
      '@langchain/core': 0.3.36
      js-tiktoken: 1.0.20
      openai: 4.104.0_zod@3.25.70
      zod: 3.25.70
      zod-to-json-schema: 3.24.6_zod@3.25.70
    transitivePeerDependencies:
      - encoding
      - ws
    dev: false

  /@langchain/textsplitters/0.1.0_@langchain+core@0.3.36:
    resolution: {integrity: sha512-djI4uw9rlkAb5iMhtLED+xJebDdAG935AdP4eRTB02R7OB/act55Bj9wsskhZsvuyQRpO4O1wQOp85s6T6GWmw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/core': '>=0.2.21 <0.4.0'
    dependencies:
      '@langchain/core': 0.3.36
      js-tiktoken: 1.0.20
    dev: false

  /@msgpackr-extract/msgpackr-extract-darwin-arm64/3.0.3:
    resolution: {integrity: sha512-QZHtlVgbAdy2zAqNA9Gu1UpIuI8Xvsd1v8ic6B2pZmeFnFcMWiPLfWXh7TVw4eGEZ/C9TH281KwhVoeQUKbyjw==}
    cpu: [arm64]
    os: [darwin]
    dev: false
    optional: true

  /@msgpackr-extract/msgpackr-extract-darwin-x64/3.0.3:
    resolution: {integrity: sha512-mdzd3AVzYKuUmiWOQ8GNhl64/IoFGol569zNRdkLReh6LRLHOXxU4U8eq0JwaD8iFHdVGqSy4IjFL4reoWCDFw==}
    cpu: [x64]
    os: [darwin]
    dev: false
    optional: true

  /@msgpackr-extract/msgpackr-extract-linux-arm/3.0.3:
    resolution: {integrity: sha512-fg0uy/dG/nZEXfYilKoRe7yALaNmHoYeIoJuJ7KJ+YyU2bvY8vPv27f7UKhGRpY6euFYqEVhxCFZgAUNQBM3nw==}
    cpu: [arm]
    os: [linux]
    dev: false
    optional: true

  /@msgpackr-extract/msgpackr-extract-linux-arm64/3.0.3:
    resolution: {integrity: sha512-YxQL+ax0XqBJDZiKimS2XQaf+2wDGVa1enVRGzEvLLVFeqa5kx2bWbtcSXgsxjQB7nRqqIGFIcLteF/sHeVtQg==}
    cpu: [arm64]
    os: [linux]
    dev: false
    optional: true

  /@msgpackr-extract/msgpackr-extract-linux-x64/3.0.3:
    resolution: {integrity: sha512-cvwNfbP07pKUfq1uH+S6KJ7dT9K8WOE4ZiAcsrSes+UY55E/0jLYc+vq+DO7jlmqRb5zAggExKm0H7O/CBaesg==}
    cpu: [x64]
    os: [linux]
    dev: false
    optional: true

  /@msgpackr-extract/msgpackr-extract-win32-x64/3.0.3:
    resolution: {integrity: sha512-x0fWaQtYp4E6sktbsdAqnehxDgEc/VwM7uLsRCYWaiGu0ykYdZPiS8zCWdnjHwyiumousxfBm4SO31eXqwEZhQ==}
    cpu: [x64]
    os: [win32]
    dev: false
    optional: true

  /@napi-rs/wasm-runtime/0.2.11:
    resolution: {integrity: sha512-9DPkXtvHydrcOsopiYpUgPHpmj0HWZKMUnL2dZqpvC42lsratuBG06V5ipyno0fUek5VlFsNQ+AcFATSrJXgMA==}
    dependencies:
      '@emnapi/core': 1.4.3
      '@emnapi/runtime': 1.4.3
      '@tybys/wasm-util': 0.9.0
    dev: true
    optional: true

  /@next/env/14.2.23:
    resolution: {integrity: sha512-CysUC9IO+2Bh0omJ3qrb47S8DtsTKbFidGm6ow4gXIG6reZybqxbkH2nhdEm1tC8SmgzDdpq3BIML0PWsmyUYA==}
    dev: false

  /@next/eslint-plugin-next/14.2.30:
    resolution: {integrity: sha512-mvVsMIutMxQ4NGZEMZ1kiBNc+la8Xmlk30bKUmCPQz2eFkmsLv54Mha8QZarMaCtSPkkFA1TMD+FIZk0l/PpzA==}
    dependencies:
      glob: 10.3.10
    dev: true

  /@next/swc-darwin-arm64/14.2.23:
    resolution: {integrity: sha512-WhtEntt6NcbABA8ypEoFd3uzq5iAnrl9AnZt9dXdO+PZLACE32z3a3qA5OoV20JrbJfSJ6Sd6EqGZTrlRnGxQQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]
    dev: false
    optional: true

  /@next/swc-darwin-x64/14.2.23:
    resolution: {integrity: sha512-vwLw0HN2gVclT/ikO6EcE+LcIN+0mddJ53yG4eZd0rXkuEr/RnOaMH8wg/sYl5iz5AYYRo/l6XX7FIo6kwbw1Q==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]
    dev: false
    optional: true

  /@next/swc-linux-arm64-gnu/14.2.23:
    resolution: {integrity: sha512-uuAYwD3At2fu5CH1wD7FpP87mnjAv4+DNvLaR9kiIi8DLStWSW304kF09p1EQfhcbUI1Py2vZlBO2VaVqMRtpg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    dev: false
    optional: true

  /@next/swc-linux-arm64-musl/14.2.23:
    resolution: {integrity: sha512-Mm5KHd7nGgeJ4EETvVgFuqKOyDh+UMXHXxye6wRRFDr4FdVRI6YTxajoV2aHE8jqC14xeAMVZvLqYqS7isHL+g==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    dev: false
    optional: true

  /@next/swc-linux-x64-gnu/14.2.23:
    resolution: {integrity: sha512-Ybfqlyzm4sMSEQO6lDksggAIxnvWSG2cDWnG2jgd+MLbHYn2pvFA8DQ4pT2Vjk3Cwrv+HIg7vXJ8lCiLz79qoQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    dev: false
    optional: true

  /@next/swc-linux-x64-musl/14.2.23:
    resolution: {integrity: sha512-OSQX94sxd1gOUz3jhhdocnKsy4/peG8zV1HVaW6DLEbEmRRtUCUQZcKxUD9atLYa3RZA+YJx+WZdOnTkDuNDNA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    dev: false
    optional: true

  /@next/swc-win32-arm64-msvc/14.2.23:
    resolution: {integrity: sha512-ezmbgZy++XpIMTcTNd0L4k7+cNI4ET5vMv/oqNfTuSXkZtSA9BURElPFyarjjGtRgZ9/zuKDHoMdZwDZIY3ehQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]
    dev: false
    optional: true

  /@next/swc-win32-ia32-msvc/14.2.23:
    resolution: {integrity: sha512-zfHZOGguFCqAJ7zldTKg4tJHPJyJCOFhpoJcVxKL9BSUHScVDnMdDuOU1zPPGdOzr/GWxbhYTjyiEgLEpAoFPA==}
    engines: {node: '>= 10'}
    cpu: [ia32]
    os: [win32]
    dev: false
    optional: true

  /@next/swc-win32-x64-msvc/14.2.23:
    resolution: {integrity: sha512-xCtq5BD553SzOgSZ7UH5LH+OATQihydObTrCTvVzOro8QiWYKdBVwcB2Mn2MLMo6DGW9yH1LSPw7jS7HhgJgjw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]
    dev: false
    optional: true

  /@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1
    dev: true

  /@nolyfill/is-core-module/1.0.39:
    resolution: {integrity: sha512-nn5ozdjYQpUCZlWGuxcJY/KpxkWQs4DcbMCmKojjyrYDEAGy4Ce19NN4v5MduafTwJlbKc99UA8YhSVqq9yPZA==}
    engines: {node: '>=12.4.0'}
    dev: true

  /@opentelemetry/api/1.9.0:
    resolution: {integrity: sha512-3giAOQvZiH5F9bMlMiv8+GSPMeqg0dbaeo58/0SlA9sxSqZhnUtxzX9/2FzyhS9sWQf5S0GJE0AKBrFqjpeYcg==}
    engines: {node: '>=8.0.0'}
    dev: false

  /@pkgjs/parseargs/0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}
    dev: true
    optional: true

  /@prisma/client/5.22.0_prisma@5.22.0:
    resolution: {integrity: sha512-M0SVXfyHnQREBKxCgyo7sffrKttwE6R8PMq330MIUF0pTwjUhLbW84pFDlf06B27XyCR++VtjugEnIHdr07SVA==}
    engines: {node: '>=16.13'}
    requiresBuild: true
    peerDependencies:
      prisma: '*'
    peerDependenciesMeta:
      prisma:
        optional: true
    dependencies:
      prisma: 5.22.0
    dev: false

  /@prisma/debug/5.22.0:
    resolution: {integrity: sha512-AUt44v3YJeggO2ZU5BkXI7M4hu9BF2zzH2iF2V5pyXT/lRTyWiElZ7It+bRH1EshoMRxHgpYg4VB6rCM+mG5jQ==}
    dev: false

  /@prisma/engines-version/5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2:
    resolution: {integrity: sha512-2PTmxFR2yHW/eB3uqWtcgRcgAbG1rwG9ZriSvQw+nnb7c4uCr3RAcGMb6/zfE88SKlC1Nj2ziUvc96Z379mHgQ==}
    dev: false

  /@prisma/engines/5.22.0:
    resolution: {integrity: sha512-UNjfslWhAt06kVL3CjkuYpHAWSO6L4kDCVPegV6itt7nD1kSJavd3vhgAEhjglLJJKEdJ7oIqDJ+yHk6qO8gPA==}
    requiresBuild: true
    dependencies:
      '@prisma/debug': 5.22.0
      '@prisma/engines-version': 5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2
      '@prisma/fetch-engine': 5.22.0
      '@prisma/get-platform': 5.22.0
    dev: false

  /@prisma/fetch-engine/5.22.0:
    resolution: {integrity: sha512-bkrD/Mc2fSvkQBV5EpoFcZ87AvOgDxbG99488a5cexp5Ccny+UM6MAe/UFkUC0wLYD9+9befNOqGiIJhhq+HbA==}
    dependencies:
      '@prisma/debug': 5.22.0
      '@prisma/engines-version': 5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2
      '@prisma/get-platform': 5.22.0
    dev: false

  /@prisma/get-platform/5.22.0:
    resolution: {integrity: sha512-pHhpQdr1UPFpt+zFfnPazhulaZYCUqeIcPpJViYoq9R+D/yw4fjE+CtnsnKzPYm0ddUbeXUzjGVGIRVgPDCk4Q==}
    dependencies:
      '@prisma/debug': 5.22.0
    dev: false

  /@radix-ui/number/1.1.1:
    resolution: {integrity: sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==}
    dev: false

  /@radix-ui/primitive/1.1.2:
    resolution: {integrity: sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==}
    dev: false

  /@radix-ui/react-accordion/1.2.11_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-l3W5D54emV2ues7jjeG1xcyN7S3jnK3zE2zHqgn0CmMsy9lNJwmgcrmaxS+7ipw15FAivzKNzH3d5EcGoFKw0A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collapsible': 1.1.11_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-alert-dialog/1.1.14_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-IOZfZ3nPvN6lXpJTBCunFQPRSvK8MDgSc1FB85xnIpUKOw9en0dJj8JmCAxV7BiZdtYlUpmrQjoTFkVYtdoWzQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dialog': 1.1.14_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-arrow/1.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-aspect-ratio/1.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-Yq6lvO9HQyPwev1onK1daHCHqXVLzPhSVjmsNjCa2Zcxy2f7uJD2itDtxknv6FzAKCwD1qQkeVDmX/cev13n/g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-avatar/1.1.10_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-V8piFfWapM5OmNCXTzVQY+E1rDa53zY+MQ4Y7356v4fFz6vqCyUtIz2rUD44ZEdwg78/jKmMJHj07+C/Z/rcog==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-is-hydrated': 0.1.0_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-checkbox/1.3.2_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-yd+dI56KZqawxKZrJ31eENUwqc1QSqg4OZ15rybGjF2ZNwMO+wCyHzAVLRp9qoYJf7kYy0YpZ2b0JCzJ42HZpA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-previous': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-size': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-collapsible/1.1.11_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-2qrRsVGSCYasSz1RFOorXwl0H7g7J1frQtgpQgYrt+MOidtPAINHn9CPovQXb83r8ahapdx3Tu0fa/pdFFSdPg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-collection/1.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-compose-refs/1.1.2_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-context-menu/2.2.15_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-UsQUMjcYTsBjTSXw0P3GO0werEQvUY2plgRQuKoCTtkNr45q1DiL51j4m7gxhABzZ0BadoXNsIbg7F3KwiUBbw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-menu': 2.1.15_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-context/1.1.2_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-dialog/1.1.14_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-focus-guards': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-focus-scope': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-remove-scroll: 2.7.1_888dbdc24f3e5b913723df3d4309886e
    dev: false

  /@radix-ui/react-direction/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-dismissable-layer/1.1.10_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-escape-keydown': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-dropdown-menu/2.1.15_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-mIBnOjgwo9AH3FyKaSWoSu/dYj6VdhJ7frEPiGTeXCdUFHjl9h3mFh2wwhEtINOmYXWhdpf1rY2minFsmaNgVQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-menu': 2.1.15_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-focus-guards/1.1.2_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-focus-scope/1.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-hover-card/1.1.14_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-CPYZ24Mhirm+g6D8jArmLzjYu4Eyg3TTUHswR26QgzXBHBe64BO/RHOJKzmF/Dxb4y4f9PKyJdwm/O/AhNkb+Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-popper': 1.2.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-id/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-label/2.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-YT1GqPSL8kJn20djelMX7/cTRp/Y9w5IZHvfxQTVHrOqa2yMl7i/UfMqKRU5V7mEyKTrUVgJXhNQPVCG8PBLoQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-menu/2.1.15_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-tVlmA3Vb9n8SZSd+YSbuFR66l87Wiy4du+YE+0hzKQEANA+7cWKH1WgqcEX4pXqxUFQKrWQGHdvEfw00TjFiew==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-focus-guards': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-focus-scope': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-popper': 1.2.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-roving-focus': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-remove-scroll: 2.7.1_888dbdc24f3e5b913723df3d4309886e
    dev: false

  /@radix-ui/react-menubar/1.1.15_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-Z71C7LGD+YDYo3TV81paUs8f3Zbmkvg6VLRQpKYfzioOE6n7fOhA3ApK/V/2Odolxjoc4ENk8AYCjohCNayd5A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-menu': 2.1.15_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-roving-focus': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-navigation-menu/1.2.13_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-WG8wWfDiJlSF5hELjwfjSGOXcBR/ZMhBFCGYe8vERpC39CQYZeq1PQ2kaYHdye3V95d06H89KGMsVCIE4LWo3g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-previous': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-visually-hidden': 1.2.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-popover/1.1.14_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-ODz16+1iIbGUfFEfKx2HTPKizg2MN39uIOV8MXeHnmdd3i/N9Wt7vU46wbHsqA0xoaQyXVcs0KIlBdOA2Y95bw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-focus-guards': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-focus-scope': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-popper': 1.2.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-remove-scroll: 2.7.1_888dbdc24f3e5b913723df3d4309886e
    dev: false

  /@radix-ui/react-popper/1.2.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@floating-ui/react-dom': 2.1.4_react-dom@18.2.0+react@18.2.0
      '@radix-ui/react-arrow': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-rect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-size': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/rect': 1.1.1
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-portal/1.1.9_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-presence/1.1.4_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-primitive/2.1.3_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-progress/1.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-vPdg/tF6YC/ynuBIJlk1mm7Le0VgW6ub6J2UWnTQ7/D23KXcPI1qy+0vBkgKgd38RCMJavBXpB83HPNFMTb0Fg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-radio-group/1.3.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-9w5XhD0KPOrm92OTTE0SysH3sYzHsSTHNvZgUBo/VZ80VdYyB5RneDbc0dKpURS24IxkoFRu/hI0i4XyfFwY6g==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-roving-focus': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-previous': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-size': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-roving-focus/1.1.10_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-scroll-area/1.2.9_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-YSjEfBXnhUELsO2VzjdtYYD4CfQjvao+lhhrX5XsHD7/cyUNzljF1FHEbgTPN7LH2MClfwRMIsYlqTYpKTTe2A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-select/2.2.5_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-HnMTdXEVuuyzx63ME0ut4+sEMYW6oouHWNGUZc7ddvUWIcfCva/AMoqEW/3wnEllriMWBa0RHspCYnfCWJQYmA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-focus-guards': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-focus-scope': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-popper': 1.2.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-previous': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-visually-hidden': 1.2.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      aria-hidden: 1.2.6
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-remove-scroll: 2.7.1_888dbdc24f3e5b913723df3d4309886e
    dev: false

  /@radix-ui/react-separator/1.1.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-slider/1.3.5_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-rkfe2pU2NBAYfGaxa3Mqosi7VZEWX5CxKaanRv0vZd4Zhl9fvQrg0VM93dv3xGLGfrHuoTRF3JXH8nb9g+B3fw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-previous': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-size': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-slot/1.2.3_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-switch/1.2.5_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-5ijLkak6ZMylXsaImpZ8u4Rlf5grRmoc0p0QeX9VJtlrM4f5m3nCTX8tWga/zOA8PZYIR/t0p2Mnvd7InrJ6yQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-previous': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-size': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-tabs/1.1.12_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-GTVAlRVrQrSw3cEARM0nAx73ixrWDPNZAruETn3oHCNP6SbZ/hNxdxp+u7VkIEv3/sFoLq1PfcHrl7Pnp0CDpw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-roving-focus': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-toast/1.2.14_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-nAP5FBxBJGQ/YfUB+r+O6USFVkWq3gAInkxyEnmvEV5jtSbfDhfa4hwX8CraCnbjMLsE7XSf/K75l9xXY7joWg==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-visually-hidden': 1.2.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-toggle-group/1.1.10_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-kiU694Km3WFLTC75DdqgM/3Jauf3rD9wxeS9XtyWFKsBUeZA337lC+6uUazT7I1DhanZ5gyD5Stf8uf2dbQxOQ==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-roving-focus': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-toggle': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-toggle/1.1.9_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-ZoFkBBz9zv9GWer7wIjvdRxmh2wyc2oKWw6C6CseWd6/yq1DK/l5lJ+wnsmFwJZbBYqr02mrf8A2q/CVCuM3ZA==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-toolbar/1.1.10_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-jiwQsduEL++M4YBIurjSa+voD86OIytCod0/dbIxFZDLD8NfO1//keXYMfsW8BPcfqwoNjt+y06XcJqAb4KR7A==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-direction': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-roving-focus': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-separator': 1.1.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-toggle-group': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-tooltip/1.2.7_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-Ap+fNYwKTYJ9pzqW+Xe2HtMRbQ/EeWkj2qykZ6SuEV4iS/o1bZI5ssJbk4D2r8XuDuOBVz/tIx2JObtuqU+5Zw==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-context': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dismissable-layer': 1.1.10_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-popper': 1.2.7_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-portal': 1.1.9_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-presence': 1.1.4_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-controllable-state': 1.2.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-visually-hidden': 1.2.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/react-use-callback-ref/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-controllable-state/1.2.2_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-effect-event/0.0.2_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-escape-keydown/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-is-hydrated/0.1.0_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-U+UORVEq+cTnRIaostJv9AGdV3G6Y+zbVd+12e18jQ5A3c0xL03IhnHuiU4UV69wolOQp5GfR58NW/EgdQhwOA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
      use-sync-external-store: 1.5.0_react@18.2.0
    dev: false

  /@radix-ui/react-use-layout-effect/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-previous/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-rect/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/rect': 1.1.1
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-use-size/1.1.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /@radix-ui/react-visually-hidden/1.2.3_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug==}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
    dependencies:
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@radix-ui/rect/1.1.1:
    resolution: {integrity: sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==}
    dev: false

  /@react-dnd/asap/5.0.2:
    resolution: {integrity: sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A==}
    dev: false

  /@react-dnd/invariant/4.0.2:
    resolution: {integrity: sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw==}
    dev: false

  /@react-dnd/shallowequal/4.0.2:
    resolution: {integrity: sha512-/RVXdLvJxLg4QKvMoM5WlwNR9ViO9z8B/qPcc+C0Sa/teJY7QG7kJ441DwzOjMYEY7GmU4dj5EcGHIkKZiQZCA==}
    dev: false

  /@rtsao/scc/1.1.0:
    resolution: {integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==}
    dev: true

  /@rushstack/eslint-patch/1.12.0:
    resolution: {integrity: sha512-5EwMtOqvJMMa3HbmxLlF74e+3/HhwBTMcvt3nqVJgGCozO6hzIPOBlwm8mGVNR9SN2IJpxSnlxczyDjcn7qIyw==}
    dev: true

  /@standard-schema/spec/1.0.0:
    resolution: {integrity: sha512-m2bOd0f2RT9k8QJx1JN85cZYyH1RqFBdlwtkSlf4tBDYLCiiZnv1fIIwacK6cqwXavOydf0NPToMQgpKq+dVlA==}
    dev: false

  /@standard-schema/spec/1.0.0-beta.4:
    resolution: {integrity: sha512-d3IxtzLo7P1oZ8s8YNvxzBUXRXojSut8pbPrTYtzsc5sn4+53jVqbk66pQerSZbZSJZQux6LkclB/+8IDordHg==}
    dev: false

  /@swc/counter/0.1.3:
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}
    dev: false

  /@swc/helpers/0.5.5:
    resolution: {integrity: sha512-KGYxvIOXcceOAbEk4bi/dVLEK9z8sZ0uBB3Il5b1rhfClSpcX0yfRO0KmTkqR2cnQDymwLB+25ZyMzICg/cm/A==}
    dependencies:
      '@swc/counter': 0.1.3
      tslib: 2.8.1
    dev: false

  /@t3-oss/env-core/0.10.1_typescript@5.8.3+zod@3.25.70:
    resolution: {integrity: sha512-GcKZiCfWks5CTxhezn9k5zWX3sMDIYf6Kaxy2Gx9YEQftFcz8hDRN56hcbylyAO3t4jQnQ5ifLawINsNgCDpOg==}
    peerDependencies:
      typescript: '>=5.0.0'
      zod: ^3.0.0
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      typescript: 5.8.3
      zod: 3.25.70
    dev: false

  /@t3-oss/env-nextjs/0.10.1_typescript@5.8.3+zod@3.25.70:
    resolution: {integrity: sha512-iy2qqJLnFh1RjEWno2ZeyTu0ufomkXruUsOZludzDIroUabVvHsrSjtkHqwHp1/pgPUzN3yBRHMILW162X7x2Q==}
    peerDependencies:
      typescript: '>=5.0.0'
      zod: ^3.0.0
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@t3-oss/env-core': 0.10.1_typescript@5.8.3+zod@3.25.70
      typescript: 5.8.3
      zod: 3.25.70
    dev: false

  /@tailwindcss/container-queries/0.1.1_tailwindcss@3.4.17:
    resolution: {integrity: sha512-p18dswChx6WnTSaJCSGx6lTmrGzNNvm2FtXmiO6AuA1V4U5REyoqwmT6kgAsIMdjo07QdAfYXHJ4hnMtfHzWgA==}
    peerDependencies:
      tailwindcss: '>=3.2.0'
    dependencies:
      tailwindcss: 3.4.17_ts-node@10.9.2
    dev: true

  /@tailwindcss/typography/0.5.16_tailwindcss@3.4.17:
    resolution: {integrity: sha512-0wDLwCVF5V3x3b1SGXPCDcdsbDHMBe+lkFzBRaHeLvNi+nrrnZ1lA18u+OTWO8iSWU2GxUOCvlXtDuqftc1oiA==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders || >=4.0.0-alpha.20 || >=4.0.0-beta.1'
    dependencies:
      lodash.castarray: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      postcss-selector-parser: 6.0.10
      tailwindcss: 3.4.17_ts-node@10.9.2
    dev: true

  /@tanstack/query-core/5.81.5:
    resolution: {integrity: sha512-ZJOgCy/z2qpZXWaj/oxvodDx07XcQa9BF92c0oINjHkoqUPsmm3uG08HpTaviviZ/N9eP1f9CM7mKSEkIo7O1Q==}
    dev: false

  /@tanstack/react-query/5.81.5_react@18.2.0:
    resolution: {integrity: sha512-lOf2KqRRiYWpQT86eeeftAGnjuTR35myTP8MXyvHa81VlomoAWNEd8x5vkcAfQefu0qtYCvyqLropFZqgI2EQw==}
    peerDependencies:
      react: ^18 || ^19
    dependencies:
      '@tanstack/query-core': 5.81.5
      react: 18.2.0
    dev: false

  /@tsconfig/node10/1.0.11:
    resolution: {integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==}
    dev: true

  /@tsconfig/node12/1.0.11:
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==}
    dev: true

  /@tsconfig/node14/1.0.3:
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==}
    dev: true

  /@tsconfig/node16/1.0.4:
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==}
    dev: true

  /@tybys/wasm-util/0.9.0:
    resolution: {integrity: sha512-6+7nlbMVX/PVDCwaIQ8nTOPveOcFLSt8GcXdx8hD0bt39uWxYT88uXzqTd4fTvqta7oeUJqudepapKNt2DYJFw==}
    dependencies:
      tslib: 2.8.1
    dev: true
    optional: true

  /@types/body-parser/1.19.6:
    resolution: {integrity: sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==}
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 20.19.4
    dev: false

  /@types/connect/3.4.38:
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}
    dependencies:
      '@types/node': 20.19.4
    dev: false

  /@types/cors/2.8.19:
    resolution: {integrity: sha512-mFNylyeyqN93lfe/9CSxOGREz8cpzAhH+E93xJ4xWQf62V8sQ/24reV2nyzUWM6H6Xji+GGHpkbLe7pVoUEskg==}
    dependencies:
      '@types/node': 20.19.4
    dev: false

  /@types/d3-array/3.2.1:
    resolution: {integrity: sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==}
    dev: false

  /@types/d3-color/3.1.3:
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}
    dev: false

  /@types/d3-ease/3.0.2:
    resolution: {integrity: sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==}
    dev: false

  /@types/d3-interpolate/3.0.4:
    resolution: {integrity: sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==}
    dependencies:
      '@types/d3-color': 3.1.3
    dev: false

  /@types/d3-path/3.1.1:
    resolution: {integrity: sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==}
    dev: false

  /@types/d3-scale/4.0.9:
    resolution: {integrity: sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==}
    dependencies:
      '@types/d3-time': 3.0.4
    dev: false

  /@types/d3-shape/3.1.7:
    resolution: {integrity: sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==}
    dependencies:
      '@types/d3-path': 3.1.1
    dev: false

  /@types/d3-time/3.0.4:
    resolution: {integrity: sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==}
    dev: false

  /@types/d3-timer/3.0.2:
    resolution: {integrity: sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==}
    dev: false

  /@types/debug/4.1.12:
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==}
    dependencies:
      '@types/ms': 2.1.0
    dev: false

  /@types/diff-match-patch/1.0.36:
    resolution: {integrity: sha512-xFdR6tkm0MWvBfO8xXCSsinYxHcqkQUlcHeSpMC2ukzOb6lwQAfDmW+Qt0AvlGd8HpsS28qKsB+oPeJn9I39jg==}
    dev: false

  /@types/eslint-scope/3.7.7:
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}
    dependencies:
      '@types/eslint': 8.56.12
      '@types/estree': 1.0.8
    dev: true

  /@types/eslint/8.56.12:
    resolution: {integrity: sha512-03ruubjWyOHlmljCVoxSuNDdmfZDzsrrz0P2LeJsOXr+ZwFQ+0yQIwNCwt/GYhV7Z31fgtXJTAEs+FYlEL851g==}
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
    dev: true

  /@types/estree/1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}
    dev: true

  /@types/express-serve-static-core/5.0.6:
    resolution: {integrity: sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==}
    dependencies:
      '@types/node': 20.19.4
      '@types/qs': 6.14.0
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.5
    dev: false

  /@types/express/5.0.3:
    resolution: {integrity: sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==}
    dependencies:
      '@types/body-parser': 1.19.6
      '@types/express-serve-static-core': 5.0.6
      '@types/serve-static': 1.15.8
    dev: false

  /@types/http-errors/2.0.5:
    resolution: {integrity: sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==}
    dev: false

  /@types/json-schema/7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}
    dev: true

  /@types/json5/0.0.29:
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}
    dev: true

  /@types/linkify-it/5.0.0:
    resolution: {integrity: sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==}
    dev: false

  /@types/lodash.debounce/4.0.9:
    resolution: {integrity: sha512-Ma5JcgTREwpLRwMM+XwBR7DaWe96nC38uCBDFKZWbNKD+osjVzdpnUSwBcqCptrp16sSOLBAUb50Car5I0TCsQ==}
    dependencies:
      '@types/lodash': 4.17.20
    dev: false

  /@types/lodash/4.17.20:
    resolution: {integrity: sha512-H3MHACvFUEiujabxhaI/ImO6gUrd8oOurg7LQtS7mbwIXA/cUqWrvBsaeJ23aZEPk1TAYkurjfMbSELfoCXlGA==}
    dev: false

  /@types/markdown-it/14.1.2:
    resolution: {integrity: sha512-promo4eFwuiW+TfGxhi+0x3czqTYJkG8qB17ZUJiVF10Xm7NLVRSLUsfRTU/6h1e24VvRnXCx+hG7li58lkzog==}
    dependencies:
      '@types/linkify-it': 5.0.0
      '@types/mdurl': 2.0.0
    dev: false

  /@types/mdast/4.0.4:
    resolution: {integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==}
    dependencies:
      '@types/unist': 3.0.3
    dev: false

  /@types/mdurl/2.0.0:
    resolution: {integrity: sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==}
    dev: false

  /@types/mime/1.3.5:
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==}
    dev: false

  /@types/ms/2.1.0:
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==}
    dev: false

  /@types/node-fetch/2.6.12:
    resolution: {integrity: sha512-8nneRWKCg3rMtF69nLQJnOYUcbafYeFSjqkw3jCRLsqkWFlHaoQrr5mXmofFGOx3DKn7UfmBMyov8ySvLRVldA==}
    dependencies:
      '@types/node': 20.19.4
      form-data: 4.0.3
    dev: false

  /@types/node/18.19.115:
    resolution: {integrity: sha512-kNrFiTgG4a9JAn1LMQeLOv3MvXIPokzXziohMrMsvpYgLpdEt/mMiVYc4sGKtDfyxM5gIDF4VgrPRyCw4fHOYg==}
    dependencies:
      undici-types: 5.26.5
    dev: false

  /@types/node/20.19.4:
    resolution: {integrity: sha512-OP+We5WV8Xnbuvw0zC2m4qfB/BJvjyCwtNjhHdJxV1639SGSKrLmJkc3fMnp2Qy8nJyHp8RO6umxELN/dS1/EA==}
    dependencies:
      undici-types: 6.21.0

  /@types/prismjs/1.26.5:
    resolution: {integrity: sha512-AUZTa7hQ2KY5L7AmtSiqxlhWxb4ina0yd8hNbl4TWuqnv/pFP0nDMb3YrfSBf4hJVGLh2YEIBfKaBW/9UEl6IQ==}
    dev: false

  /@types/prop-types/15.7.15:
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==}
    dev: true

  /@types/qs/6.14.0:
    resolution: {integrity: sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==}
    dev: false

  /@types/range-parser/1.2.7:
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==}
    dev: false

  /@types/react-dom/18.3.7_@types+react@18.3.23:
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==}
    peerDependencies:
      '@types/react': ^18.0.0
    dependencies:
      '@types/react': 18.3.23
    dev: true

  /@types/react/18.3.23:
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==}
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3
    dev: true

  /@types/retry/0.12.0:
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==}
    dev: false

  /@types/send/0.17.5:
    resolution: {integrity: sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==}
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 20.19.4
    dev: false

  /@types/serve-static/1.15.8:
    resolution: {integrity: sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==}
    dependencies:
      '@types/http-errors': 2.0.5
      '@types/node': 20.19.4
      '@types/send': 0.17.5
    dev: false

  /@types/unist/3.0.3:
    resolution: {integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==}
    dev: false

  /@types/uuid/10.0.0:
    resolution: {integrity: sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==}
    dev: false

  /@types/webpack/5.28.5:
    resolution: {integrity: sha512-wR87cgvxj3p6D0Crt1r5avwqffqPXUkNlnQ1mjU93G7gCuFjufZR4I6j8cz5g1F1tTYpfOOFvly+cmIQwL9wvw==}
    dependencies:
      '@types/node': 20.19.4
      tapable: 2.2.2
      webpack: 5.99.9
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
      - webpack-cli
    dev: true

  /@typescript-eslint/eslint-plugin/7.18.0_ec52852665279724a7bf6969b667668f:
    resolution: {integrity: sha512-94EQTWZ40mzBc42ATNIBimBEDltSJ9RQHCC8vc/PDbxi4k8dVwUAv4o98dk50M1zB+JGFxp43FP7f8+FP8R6Sw==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^7.0.0
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 7.18.0_eslint@8.57.1+typescript@5.8.3
      '@typescript-eslint/scope-manager': 7.18.0
      '@typescript-eslint/type-utils': 7.18.0_eslint@8.57.1+typescript@5.8.3
      '@typescript-eslint/utils': 7.18.0_eslint@8.57.1+typescript@5.8.3
      '@typescript-eslint/visitor-keys': 7.18.0
      eslint: 8.57.1
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      ts-api-utils: 1.4.3_typescript@5.8.3
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/parser/7.18.0_eslint@8.57.1+typescript@5.8.3:
    resolution: {integrity: sha512-4Z+L8I2OqhZV8qA132M4wNL30ypZGYOQVBfMgxDH/K5UX0PNqTu1c6za9ST5r9+tavvHiTWmBnKzpCJ/GlVFtg==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/scope-manager': 7.18.0
      '@typescript-eslint/types': 7.18.0
      '@typescript-eslint/typescript-estree': 7.18.0_typescript@5.8.3
      '@typescript-eslint/visitor-keys': 7.18.0
      debug: 4.4.1
      eslint: 8.57.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/scope-manager/7.18.0:
    resolution: {integrity: sha512-jjhdIE/FPF2B7Z1uzc6i3oWKbGcHb87Qw7AWj6jmEqNOfDFbJWtjt/XfwCpvNkpGWlcJaog5vTR+VV8+w9JflA==}
    engines: {node: ^18.18.0 || >=20.0.0}
    dependencies:
      '@typescript-eslint/types': 7.18.0
      '@typescript-eslint/visitor-keys': 7.18.0
    dev: true

  /@typescript-eslint/type-utils/7.18.0_eslint@8.57.1+typescript@5.8.3:
    resolution: {integrity: sha512-XL0FJXuCLaDuX2sYqZUUSOJ2sG5/i1AAze+axqmLnSkNEVMVYLF+cbwlB2w8D1tinFuSikHmFta+P+HOofrLeA==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      eslint: ^8.56.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/typescript-estree': 7.18.0_typescript@5.8.3
      '@typescript-eslint/utils': 7.18.0_eslint@8.57.1+typescript@5.8.3
      debug: 4.4.1
      eslint: 8.57.1
      ts-api-utils: 1.4.3_typescript@5.8.3
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/types/7.18.0:
    resolution: {integrity: sha512-iZqi+Ds1y4EDYUtlOOC+aUmxnE9xS/yCigkjA7XpTKV6nCBd3Hp/PRGGmdwnfkV2ThMyYldP1wRpm/id99spTQ==}
    engines: {node: ^18.18.0 || >=20.0.0}
    dev: true

  /@typescript-eslint/typescript-estree/7.18.0_typescript@5.8.3:
    resolution: {integrity: sha512-aP1v/BSPnnyhMHts8cf1qQ6Q1IFwwRvAQGRvBFkWlo3/lH29OXA3Pts+c10nxRxIBrDnoMqzhgdwVe5f2D6OzA==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 7.18.0
      '@typescript-eslint/visitor-keys': 7.18.0
      debug: 4.4.1
      globby: 11.1.0
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 1.4.3_typescript@5.8.3
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/utils/7.18.0_eslint@8.57.1+typescript@5.8.3:
    resolution: {integrity: sha512-kK0/rNa2j74XuHVcoCZxdFBMF+aq/vH83CXAOHieC+2Gis4mF8jJXT5eAfyD3K0sAxtPuwxaIOIOvhwzVDt/kw==}
    engines: {node: ^18.18.0 || >=20.0.0}
    peerDependencies:
      eslint: ^8.56.0
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0_eslint@8.57.1
      '@typescript-eslint/scope-manager': 7.18.0
      '@typescript-eslint/types': 7.18.0
      '@typescript-eslint/typescript-estree': 7.18.0_typescript@5.8.3
      eslint: 8.57.1
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /@typescript-eslint/visitor-keys/7.18.0:
    resolution: {integrity: sha512-cDF0/Gf81QpY3xYyJKDV14Zwdmid5+uuENhjH2EqFaF0ni+yAyq/LzMaIJdhNJXZI7uLzwIlA+V7oWoyn6Curg==}
    engines: {node: ^18.18.0 || >=20.0.0}
    dependencies:
      '@typescript-eslint/types': 7.18.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@udecode/cmdk/0.1.1_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-1o3dCDvH7bMoB7PInSfsRetxoVBxK7cuIZDM+DhaWj/ZT40rMtQkXdb7RqVh3H2j/xH7JoaKK7kIi9nlscZDfg==}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      react-dom: ^18 || ^19 || ^19.0.0-rc
    dependencies:
      '@radix-ui/react-dialog': 1.1.14_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      use-sync-external-store: 1.5.0_react@18.2.0
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
    dev: false

  /@udecode/cn/40.2.8_db7f2e5930f556bc9133bdfa61b2ae1e:
    resolution: {integrity: sha512-mDsQhNr/Dzz1htEIh0NO37Fn7oyAGzkU8Gfj19oIJ/ULQ0pr61/hm69cCbSl3EtdEm3ROpycgvG2uezLZzJilA==}
    peerDependencies:
      class-variance-authority: '>=0.7.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      tailwind-merge: '>=2.2.0'
    dependencies:
      '@udecode/react-utils': 40.2.8_15a31d56078ea75206a535ec438a66d6
      class-variance-authority: 0.7.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      tailwind-merge: 2.6.0
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /@udecode/plate-ai/41.0.14_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-jR1xxx5zcUsK+pS7Viw6R8MMnkVdhAr/DtBOvenoKSXh+b4Hyb/ZrYYugw4BS/PaGJv6HoJnt+dOInrM2rn8Hg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-markdown': 41.0.14_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-selection': 41.0.8_1ea4ed1c6b7f43d4e7139d1718ef6780
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@udecode/plate-alignment/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-1C39PO5QiNPzoSg8tEyiCVbrr6mv4QF4MoVvolpor0kZ5HIMcjUPQ59CVBST9O/7fh75tavGsaDjznxJiX0EFg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-autoformat/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-MqeVrEkIs7TfbVIgClbY+ca5GnD4ADeIoQAZjwKtfuax4eE1kX9FySKo+ZjTclRyO9PQZ3KSMy0vbSNVl63pJQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-basic-elements/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-nk1qCLKcTwItX5q8TOwquGKp1NMOQr+Ozdt9anGVx3jayqXcSzB3876GjMaAc3HIsCY+x/aQIA9t6cnAmWMYaw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-block-quote': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-code-block': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-heading': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-basic-marks/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-iSpbiorQoBYtIXl8HGQ+Pz2lkd1LHNJ79FiUWPyjEDwNduuIUF6My0pNAjYnQ8bulYl98192wWwrjGcSAlX0kg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-block-quote/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-a1dj0T4N/AJvxp+oo51mZxjDIVB2rf7dqy13KO2OAbhuOwfsxlTbTqjdLAQFvTQaZWB/BkCkZHZQ0X8obrDjHQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-break/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-GYN3bBXRy9ws3tMQXy/hiXFhLkTQESOBmn82vI2tvyfRtNO+QqZIv3hERC5qnBN51LYAwLTDQSQMRWx5MJRcyQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-callout/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-V9ap8qCZX6pqK4EysupTWymInx/69G+5J19uvpmz5NZrqiS7d4w2jNGvH14ZU17Xr51ZmV6c12Pb+Lm8bZuHww==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-caption/41.0.0_7a26c35e4a848caccaecf24742ce5138:
    resolution: {integrity: sha512-EQfR7hPC8isSV1MLuZe9YW7XO+YaLuJe3uxIlvg2rnFiDkvmLCtk1SdSZ8y37IDq9yUxXZiGpOVhX6VQmY43FA==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-textarea-autosize: 8.5.9_888dbdc24f3e5b913723df3d4309886e
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /@udecode/plate-code-block/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-mpUXPTyh4NsOFwMYzyCh3VQpEv8cxy0vrTvWIfs06ZmGMvYX+i1Qi03cR4zojdwI8m6XI7v0oLMqnej/n2a8uA==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-combobox/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-mEG+VVuXU3E10HT76MBE08A2HnXAAge6zv96KzcM4+L3k0GVZ/S11eP8kVgIj8rLUOvbr9E/vHU7ulQdSwdQSg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-comments/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-OoABlxyD/rRRQ+sMSog8UhtQuPp6mMWekiLInpSf+pqJAa7WCQB7l50CyptjBdz/A5JHindSRzfukyOqt6v3qQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-common/41.0.13_5375e8c3125b784b45e0c8f07e4fcfda:
    resolution: {integrity: sha512-QuxdWo+YGWolS7TYGmuKjmeUrDavqiGetfmVFvazx3m0v2baDrdq5iqKwQDoWLevROnQorXLbNfvronEwDu5BQ==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-core': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-utils': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/react-hotkeys': 37.0.0_react-dom@18.2.0+react@18.2.0
      '@udecode/react-utils': 40.2.8_15a31d56078ea75206a535ec438a66d6
      '@udecode/slate': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/slate-react': 41.0.5_46ce19f948c399afde8591efe684cf7d
      '@udecode/slate-utils': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/utils': 37.0.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - '@types/react'
      - immer
      - react-native
      - scheduler
    dev: false

  /@udecode/plate-core/41.0.13_5375e8c3125b784b45e0c8f07e4fcfda:
    resolution: {integrity: sha512-UkmkoLXA2w41dIjTXCiMjatvPcAtwbCTNfIRiNE2QxhqNQGa+RuNGUgTV0oYR6MHsnI0WatrUH5v7sIh2hm3zA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/react-hotkeys': 37.0.0_react-dom@18.2.0+react@18.2.0
      '@udecode/react-utils': 40.2.8_15a31d56078ea75206a535ec438a66d6
      '@udecode/slate': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/slate-react': 41.0.5_46ce19f948c399afde8591efe684cf7d
      '@udecode/slate-utils': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/utils': 37.0.0
      clsx: 2.1.1
      html-entities: 2.6.0
      is-hotkey: 0.2.0
      jotai: 2.8.4_888dbdc24f3e5b913723df3d4309886e
      jotai-optics: 0.4.0_jotai@2.8.4+optics-ts@2.4.1
      jotai-x: 1.2.4_5d95b62253ad84e67439488ad15d42b3
      lodash: 4.17.21
      nanoid: 3.3.11
      optics-ts: 2.4.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
      use-deep-compare: 1.3.0_react@18.2.0
      zustand: 4.5.7_888dbdc24f3e5b913723df3d4309886e
      zustand-x: 3.0.4_99192c02dfac84441cb35d79f6436f58
    transitivePeerDependencies:
      - '@types/react'
      - immer
      - react-native
      - scheduler
    dev: false

  /@udecode/plate-csv/41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-iU8AduDmbwqjeSNdK3bIq3H9QxvL/EzLZEtzJQ9LXUpYPXJiuoG1QnhytL187td4GmoDDrD6Kb6g0sJMZhqZuA==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-table': 41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780
      papaparse: 5.5.3
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-date/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-+LabWCpo7KWmVY2InVCv7NR3ROY+prLPZ8nhPmMOesKiKGsnMwZmHppgotl/8OS+h/xQCJ3nhVMs8uGsrGaXMg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.94.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.99.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-diff/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-YkXstNovtipDvLTv9NtpnSPAAF3aY5jzztL78G/FejSwDAcuxeGUdvIPWN92AQZrmGubZNTQJCgUPMSI1HWHzQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      diff-match-patch-ts: 0.6.0
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-dnd/41.0.2_68ca63d7af8cbe90af517060dbbd08a6:
    resolution: {integrity: sha512-K9SlxGeyDFacHZIS4lfHRrCzAujhWHYokzS2GPOu9dDeL70G7JtXrQtkPFCZ7trfgTBp8WJ3KggWc3qSDnY0XA==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.2'
      react: '>=16.8.0'
      react-dnd: '>=14.0.0'
      react-dnd-html5-backend: '>=14.0.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      raf: 3.4.1
      react: 18.2.0
      react-dnd: 16.0.1_fef9396d7cc8c8d91fc915a58da7a30e
      react-dnd-html5-backend: 16.0.1
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-docx/41.0.10_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-3cl38QB21TMFL1PZKJnsKJVfSkopoSdGxCZbPpXhzq+Yx8nIvGFKDLE2AvfN6qopJi9ORIixlO0nGEy6ho4SSQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-heading': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-indent': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-indent-list': 41.0.10_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-media': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-table': 41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
      validator: 13.15.15
    dev: false

  /@udecode/plate-emoji/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-+JSUnxxgH1ZNymTwfNrX8We3LswCYKELWWSSiIKMVwXrh8unwObcTXNayS29gq495DOwsVlw1PdNJ4dcR1SgGw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@emoji-mart/data': 1.2.1
      '@udecode/plate-combobox': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-excalidraw/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-VLvLVHn3/nSaKKulgGZk7jEsL31rpx9o8c2L9o8R6OcSfK28GgOP6xmR+/k/jpnt2u3sNkXLzS47rNYQiwLu6Q==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@excalidraw/excalidraw': 0.16.4_react-dom@18.2.0+react@18.2.0
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-find-replace/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-TpyXVc3CTwUr73rpY75057f7dosU/3a8Ajb4UQlIrvvAxg6QbCM52lML3X19w8sx0Z6/Vr4Qky/ke4yHT4HLIw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-floating/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-S0NxLzJPFWwj+A4bX6XgERNUn025DKpNfyw+ReoiGW8MGggVWMF7fwbfU3dZa88gqq9rxkh9rUkDHSAGQKkkWw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/react': 0.26.28_react-dom@18.2.0+react@18.2.0
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-font/41.0.12_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-voD6P7o4lfCcq/msOLL6tcYBbYwFEbhoeY99AXBmnL2CxqZhHbM5BfKbK4D2AhAM0DXY630V66XOrG5mXjVcug==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-heading/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-8SVS1B7f62RNcptYVSUGTDVSpDeSnFZZFg9TOEU7Q2H6WcuK4BP5MXXJusmFoaO3qtbJgch4xipLpDkLJzOJPw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-highlight/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-aSqox99BaUs2/Z0yQeXgoYWqIVGJkCH31JaQHNivkPpjL3omYrISFjvU/RJElqbbZS0LrS9zSvJaHLjFA8J2SQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-horizontal-rule/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-x7TCrqytNeTSU91FdGLHhq1SgyP92BIgr5+891P5F9d55acYxAcn2XJ63ldnHYIme+b5vDyVaIz9jrOnmvgL+w==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-indent-list/41.0.10_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-iiUlkCo8TLmCjvYtPZ5TLzlDctUQg9m9b12fjIUdvCJtGU/6+jZArzJBSToZTKU30c9dz1W2W0qxbbspCJiRdA==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-indent': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-list': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      clsx: 2.1.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-indent/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-N7tDCNSGhFQAfQ0Ip550xhlOmYJRhm8hcdYAKzYNeX9oXP2Jsf2DEs+nEgZzvCjGWg1PPdN+tu2wZ/zNHnUC8w==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-juice/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-XXz9zLeC7hc4VEQEbBkV4ukeOk0IkeyCjXnfR9LhqdJQncajeWFZl/KsxO4svLol4bWob/m90yw8eyQRB65aOg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      juice: 8.1.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - encoding
    dev: false

  /@udecode/plate-kbd/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-eGlgO/wNpxIpHleIm0hxldxuC6o6wvBq35LfUeM9LuAJGyVwSzStul1W57weDvf3ICp9pvbBQMyoxreAdDjqJQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-layout/41.0.2_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-kjrr0LHKi2V8W6W/55jlWquOWIA7JL1DVT+pBCY8hJdwBa0rCayKiHgDW4RXWVfDZ+uHndll9hY7uU5USw1N7A==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.2'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-line-height/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-VwMxnaJ6V6SMPeI4XvNMNujBPIG24Q5/OwiDojfGqz8IGfp8UDAyWsXArXybyAd1f/Gcv+6dj6uh5JuIGmqM9Q==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-link/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-9+ZAMwpjCWXY3n6tR96nTQQgK0QbHP4XZaCgIHv2vuRyecbOXrgsHpOgb8BbkQk84IXkzXEznacjVuyEr/T46Q==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-floating': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-normalizers': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-list/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-KnuMJ/NguM4+WFAU4sQdXURtbHoh9hNiCjzKdQVFwA91Fszwfrc6pKmWtpeT5nAGdRFu0s9Zd7fUKxaI7/0tYg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-reset-node': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-markdown/41.0.14_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-L6pwhfRU17uzugWYkHOpGxjwl3tii95HwmpDNrqAj/L8GtXgyghQqfZcdPhZf+1mmGejoBoQFy+f3G/JLFJgRg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.13'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      remark-gfm: 4.0.0
      remark-parse: 11.0.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@udecode/plate-math/41.0.11_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-X2zAlskM9evdZTe0I6INPBBbHR+ED5WsR0fu2JaQYNAAr33RBsogl+aftQkC7u+TaZ12cJ5lF6iMW+zLSeg5pQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      katex: 0.16.11
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-media/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-PkF9r+Nl3JrUpfk3GVvxPMMPbSIkoSeeApbjqcME5kPJfKylumq5jjjDB7v3iFCsEpyZmwdVoEoVfDhuQSGWeg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      js-video-url-parser: 0.5.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-mention/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-seRGbbfTdeqTwcz/LkVY6l7Vt1b8K6YwrUtAwQhSF3WR0P3B6fAVX1dzpMRqX6t7kgFA5OgDRhylGZ3OwMQRPg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-combobox': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-node-id/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-YSrgFh5cMnHCQFd02zpJljNXTAIwJox6q8bR09dTepoREW+4V/wQTFG1ubS2w/Irxqm/59PtWQQiDzlpow++tw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-normalizers/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-nuFLHv/I0JJHFVlA5OOH98fjQM32H1MnWyhzxc1JYWdwiHFpWJAcyF0eg1ue2+x3iZGx5SKhWzNHoUH+xUZvig==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-reset-node/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-CF6E37HHhFbFkHl1chdOyzOP1+PHfMQG09+w7Me2xqhAKALUk9AL4k1VYOdSJD6TrkA+YCgPdaqdm9e0Xgf36g==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-resizable/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-UExSRoA9uDHn/TCG29ySwJoT8cd3NQhwNcWIHQVZ5PYc+2s+G0XDLRLHBPKugzVqgRgaZBWHJ6GdRwLZaurFVw==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-select/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-pkCIEWy83fraRZEJKIzrJojM1Wx7xFBWuVTnWuAEV/xjTcM60d5wisPePNPZEcTdzZzuVq8grBOT3Vk2NRmW1A==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-selection/41.0.8_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-s4fxBHRebNpHzE65r7m1vkLNxP1uwJaXmTa8YxAXWTPrUIbWB++mmnTDQ2bPz0O5w1p0TDI5xdf8FRYV/WMmAQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      copy-to-clipboard: 3.3.3
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-slash-command/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-dAssRGV1VuFbNJIil22FHs2uKvVIsFNowgQzZLW0il/mZpUKH8U8bTHmTqbcP3fB7HRgYYiT3semPnEXRsQBgg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-combobox': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-suggestion/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-wKbfHj5+LJTqkbmgN2EhSlkRphzugw5zeJ3x1jMI900q960lyKk+7iRwnAU+FErNCWSmXy+0I69IvECVRDgk3w==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-diff': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-tabbable/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-9gjdldiYdHYUnSAoTexZIj1hpPvoGhqDSwWxUHMpJ2XzTHJPBCkgvi2aUPRAozzmgQTlYtljmdo14+Earddong==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
      tabbable: 6.2.0
    dev: false

  /@udecode/plate-table/41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-6qYe6TX9i1rU3BdJMoF9TARVIl5mIb530pi5wHzu/HNwAxXi7YGNM3rTACFMcM44q3cX0PmiINy195cve8oLIQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.5'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-resizable': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - slate-hyperscript
    dev: false

  /@udecode/plate-toggle/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-uY9yvbs2QgEAPdum+AKhTPZagPhqwYreuUjo2VSXnZB6iSD1LwtLAxpi4ViVV3ZN2JFKKtz31CBG/kjS0jwfEg==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-indent': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-node-id': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-trailing-block/41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780:
    resolution: {integrity: sha512-RF9ee111450blYAI5LoGupkvciNZmR7idnrrkuyjOmVoYTPkGX73jUWBolScQDzVZJvCrOZFuaT0I9VcNdArwQ==}
    peerDependencies:
      '@udecode/plate-common': '>=41.0.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    dev: false

  /@udecode/plate-utils/41.0.13_5375e8c3125b784b45e0c8f07e4fcfda:
    resolution: {integrity: sha512-VCk0cFayLlvevZmkKIDLXg/gZTZ7sq53SBNljllBHTUbqede9BXM+cNBlaNEjmvidU8AwJGkPn4W4iLmp9INvA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.110.0'
    dependencies:
      '@udecode/plate-core': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/react-utils': 40.2.8_15a31d56078ea75206a535ec438a66d6
      '@udecode/slate': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/slate-react': 41.0.5_46ce19f948c399afde8591efe684cf7d
      '@udecode/slate-utils': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/utils': 37.0.0
      clsx: 2.1.1
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - '@types/react'
      - immer
      - react-native
      - scheduler
      - slate-dom
    dev: false

  /@udecode/plate/41.0.14_5375e8c3125b784b45e0c8f07e4fcfda:
    resolution: {integrity: sha512-sL2EbHXOohVHxmV9VWBoHVxEr2jQGH27AHX6TwDoWk1cx2yWi24S78lRHN6WptM+F8btxp9KpindqB5q8DD4MA==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-dom: '>=0.111.0'
      slate-history: '>=0.93.0'
      slate-hyperscript: '>=0.66.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/plate-alignment': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-autoformat': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-basic-elements': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-basic-marks': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-block-quote': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-break': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-code-block': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-combobox': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-comments': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-common': 41.0.13_5375e8c3125b784b45e0c8f07e4fcfda
      '@udecode/plate-csv': 41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-diff': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-docx': 41.0.10_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-find-replace': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-floating': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-font': 41.0.12_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-heading': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-highlight': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-horizontal-rule': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-indent': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-indent-list': 41.0.10_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-kbd': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-layout': 41.0.2_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-line-height': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-link': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-list': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-markdown': 41.0.14_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-media': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-mention': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-node-id': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-normalizers': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-reset-node': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-resizable': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-select': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-selection': 41.0.8_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-slash-command': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-suggestion': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-tabbable': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-table': 41.0.9_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-toggle': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      '@udecode/plate-trailing-block': 41.0.0_1ea4ed1c6b7f43d4e7139d1718ef6780
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-hyperscript: 0.100.0_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - '@types/react'
      - immer
      - react-native
      - scheduler
      - supports-color
    dev: false

  /@udecode/react-hotkeys/37.0.0_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-3ZV5LiaTnKyhXwN6U0NE2cofNsNN2IPMkNCDntbSIIRLYmI+o6LRkDwAucSNh/BIdNXfvxscsR04RYyIwjGbJw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /@udecode/react-utils/40.2.8_15a31d56078ea75206a535ec438a66d6:
    resolution: {integrity: sha512-Lv8sd6gKSJHbZ0vIVK6rDBwFktAs1q2pfFZ1Ocx5xtLBtINzjjs7TvwbTb8uzVWTTUI2QxuoHDiY2dfTSYWlAQ==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      '@radix-ui/react-slot': 1.2.3_888dbdc24f3e5b913723df3d4309886e
      '@udecode/utils': 37.0.0
      clsx: 2.1.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /@udecode/slate-react/41.0.5_46ce19f948c399afde8591efe684cf7d:
    resolution: {integrity: sha512-3UeFI/jOw0IQ2kZcsa4um5iL/UBBPOgHapyK3jcJky8wNzkYTb1NJRr8m3jUGnL/Y2h/Uf5NRvh0gxCdNHj/XQ==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      slate: '>=0.112.0'
      slate-history: '>=0.93.0'
      slate-react: '>=0.111.0'
    dependencies:
      '@udecode/react-utils': 40.2.8_15a31d56078ea75206a535ec438a66d6
      '@udecode/slate': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/utils': 37.0.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
      slate-react: 0.110.3_23b6d8887d45f82da5f0400dcb42c149
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /@udecode/slate-utils/41.0.0_3373cc44976f8882c271ef3cdeeb83b7:
    resolution: {integrity: sha512-aQp5xXrLkuffrTbyRitNdcxt4jwjIQOKYXTv5AjwWhq2zoDCtabooZcQQvuCXdYpnNCWBopu4TI6Ac5EtR3h7A==}
    peerDependencies:
      slate: '>=0.112.0'
      slate-history: '>=0.93.0'
    dependencies:
      '@udecode/slate': 41.0.0_3373cc44976f8882c271ef3cdeeb83b7
      '@udecode/utils': 37.0.0
      lodash: 4.17.21
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
    dev: false

  /@udecode/slate/41.0.0_3373cc44976f8882c271ef3cdeeb83b7:
    resolution: {integrity: sha512-2YiEEgibwOmZ3b1SmPJqN9VnEM66h6km2L73rVffmLayIttiIwcX2hL4AczY6nsSK6CdN9S7EWwUifVsnXCSqg==}
    peerDependencies:
      slate: '>=0.112.0'
      slate-history: '>=0.93.0'
    dependencies:
      '@udecode/utils': 37.0.0
      is-plain-object: 5.0.0
      slate: 0.103.0
      slate-history: 0.113.1_slate@0.103.0
    dev: false

  /@udecode/utils/37.0.0:
    resolution: {integrity: sha512-30ixi2pznIXyIqpFocX+X5Sj38js+wZ0RLY14eZv1C1zwWo5BxSuJfzpGQTvGcLPJnij019tEpmGH61QdDxtrQ==}
    dev: false

  /@ungap/structured-clone/1.3.0:
    resolution: {integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==}
    dev: true

  /@unrs/resolver-binding-android-arm-eabi/1.10.1:
    resolution: {integrity: sha512-zohDKXT1Ok0yhbVGff4YAg9HUs5ietG5GpvJBPFSApZnGe7uf2cd26DRhKZbn0Be6xHUZrSzP+RAgMmzyc71EA==}
    cpu: [arm]
    os: [android]
    dev: true
    optional: true

  /@unrs/resolver-binding-android-arm64/1.10.1:
    resolution: {integrity: sha512-tAN6k5UrTd4nicpA7s2PbjR/jagpDzAmvXFjbpTazUe5FRsFxVcBlS1F5Lzp5jtWU6bdiqRhSvd4X8rdpCffeA==}
    cpu: [arm64]
    os: [android]
    dev: true
    optional: true

  /@unrs/resolver-binding-darwin-arm64/1.10.1:
    resolution: {integrity: sha512-+FCsag8WkauI4dQ50XumCXdfvDCZEpMUnvZDsKMxfOisnEklpDFXc6ThY0WqybBYZbiwR5tWcFaZmI0G6b4vrg==}
    cpu: [arm64]
    os: [darwin]
    dev: true
    optional: true

  /@unrs/resolver-binding-darwin-x64/1.10.1:
    resolution: {integrity: sha512-qYKGGm5wk71ONcXTMZ0+J11qQeOAPz3nw6VtqrBUUELRyXFyvK8cHhHsLBFR4GHnilc2pgY1HTB2TvdW9wO26Q==}
    cpu: [x64]
    os: [darwin]
    dev: true
    optional: true

  /@unrs/resolver-binding-freebsd-x64/1.10.1:
    resolution: {integrity: sha512-hOHMAhbvIQ63gkpgeNsXcWPSyvXH7ZEyeg254hY0Lp/hX8NdW+FsUWq73g9946Pc/BrcVI/I3C1cmZ4RCX9bNw==}
    cpu: [x64]
    os: [freebsd]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-arm-gnueabihf/1.10.1:
    resolution: {integrity: sha512-6ds7+zzHJgTDmpe0gmFcOTvSUhG5oZukkt+cCsSb3k4Uiz2yEQB4iCRITX2hBwSW+p8gAieAfecITjgqCkswXw==}
    cpu: [arm]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-arm-musleabihf/1.10.1:
    resolution: {integrity: sha512-P7A0G2/jW00diNJyFeq4W9/nxovD62Ay8CMP4UK9OymC7qO7rG1a8Upad68/bdfpIOn7KSp7Aj/6lEW3yyznAA==}
    cpu: [arm]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-arm64-gnu/1.10.1:
    resolution: {integrity: sha512-Cg6xzdkrpltcTPO4At+A79zkC7gPDQIgosJmVV8M104ImB6KZi1MrNXgDYIAfkhUYjPzjNooEDFRAwwPadS7ZA==}
    cpu: [arm64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-arm64-musl/1.10.1:
    resolution: {integrity: sha512-aNeg99bVkXa4lt+oZbjNRPC8ZpjJTKxijg/wILrJdzNyAymO2UC/HUK1UfDjt6T7U5p/mK24T3CYOi3/+YEQSA==}
    cpu: [arm64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-ppc64-gnu/1.10.1:
    resolution: {integrity: sha512-ylz5ojeXrkPrtnzVhpCO+YegG63/aKhkoTlY8PfMfBfLaUG8v6m6iqrL7sBUKdVBgOB4kSTUPt9efQdA/Y3Z/w==}
    cpu: [ppc64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-riscv64-gnu/1.10.1:
    resolution: {integrity: sha512-xcWyhmJfXXOxK7lvE4+rLwBq+on83svlc0AIypfe6x4sMJR+S4oD7n9OynaQShfj2SufPw2KJAotnsNb+4nN2g==}
    cpu: [riscv64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-riscv64-musl/1.10.1:
    resolution: {integrity: sha512-mW9JZAdOCyorgi1eLJr4gX7xS67WNG9XNPYj5P8VuttK72XNsmdw9yhOO4tDANMgiLXFiSFaiL1gEpoNtRPw/A==}
    cpu: [riscv64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-s390x-gnu/1.10.1:
    resolution: {integrity: sha512-NZGKhBy6xkJ0k09cWNZz4DnhBcGlhDd3W+j7EYoNvf5TSwj2K6kbmfqTWITEgkvjsMUjm1wsrc4IJaH6VtjyHQ==}
    cpu: [s390x]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-x64-gnu/1.10.1:
    resolution: {integrity: sha512-VsjgckJ0gNMw7p0d8In6uPYr+s0p16yrT2rvG4v2jUpEMYkpnfnCiALa9SWshbvlGjKQ98Q2x19agm3iFk8w8Q==}
    cpu: [x64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-linux-x64-musl/1.10.1:
    resolution: {integrity: sha512-idMnajMeejnaFi0Mx9UTLSYFDAOTfAEP7VjXNgxKApso3Eu2Njs0p2V95nNIyFi4oQVGFmIuCkoznAXtF/Zbmw==}
    cpu: [x64]
    os: [linux]
    dev: true
    optional: true

  /@unrs/resolver-binding-wasm32-wasi/1.10.1:
    resolution: {integrity: sha512-7jyhjIRNFjzlr8x5pth6Oi9hv3a7ubcVYm2GBFinkBQKcFhw4nIs5BtauSNtDW1dPIGrxF0ciynCZqzxMrYMsg==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    dependencies:
      '@napi-rs/wasm-runtime': 0.2.11
    dev: true
    optional: true

  /@unrs/resolver-binding-win32-arm64-msvc/1.10.1:
    resolution: {integrity: sha512-TY79+N+Gkoo7E99K+zmsKNeiuNJYlclZJtKqsHSls8We2iGhgxtletVsiBYie93MSTDRDMI8pkBZJlIJSZPrdA==}
    cpu: [arm64]
    os: [win32]
    dev: true
    optional: true

  /@unrs/resolver-binding-win32-ia32-msvc/1.10.1:
    resolution: {integrity: sha512-BAJN5PEPlEV+1m8+PCtFoKm3LQ1P57B4Z+0+efU0NzmCaGk7pUaOxuPgl+m3eufVeeNBKiPDltG0sSB9qEfCxw==}
    cpu: [ia32]
    os: [win32]
    dev: true
    optional: true

  /@unrs/resolver-binding-win32-x64-msvc/1.10.1:
    resolution: {integrity: sha512-2v3erKKmmCyIVvvhI2nF15qEbdBpISTq44m9pyd5gfIJB1PN94oePTLWEd82XUbIbvKhv76xTSeUQSCOGesLeg==}
    cpu: [x64]
    os: [win32]
    dev: true
    optional: true

  /@uploadthing/mime-types/0.3.5:
    resolution: {integrity: sha512-iYOmod80XXOSe4NVvaUG9FsS91YGPUaJMTBj52Nwu0G2aTzEN6Xcl0mG1rWqXJ4NUH8MzjVqg+tQND5TPkJWhg==}
    dev: false

  /@uploadthing/react/7.3.2_95e3019cd4c89d97ff3e5bf49022e184:
    resolution: {integrity: sha512-dssVzrxGBKBHUzJu/CiEGc3hQ49U4sPdnqN5xMmtEdnJP+6OB+a8JUjkwxiJDBQXc3ft9XCmkLCZZoUt1EQSIw==}
    peerDependencies:
      next: '*'
      react: ^17.0.2 || ^18.0.0 || ^19.0.0
      uploadthing: ^7.2.0
    peerDependenciesMeta:
      next:
        optional: true
    dependencies:
      '@uploadthing/shared': 7.1.9
      file-selector: 0.6.0
      next: 14.2.23_react-dom@18.2.0+react@18.2.0
      react: 18.2.0
      uploadthing: 7.7.3_next@14.2.23+tailwindcss@3.4.17
    dev: false

  /@uploadthing/shared/7.1.9:
    resolution: {integrity: sha512-5Gn1wGVSygsBxI6tjOwwEQt/U4m+vbmZCnsuf8pDfZ+MiXe3el03CWMmpbH3KtSu0BwG48wyCKNfHplZsphvOA==}
    dependencies:
      '@uploadthing/mime-types': 0.3.5
      effect: 3.16.8
      sqids: 0.3.0
    dev: false

  /@webassemblyjs/ast/1.14.1:
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==}
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
    dev: true

  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==}
    dev: true

  /@webassemblyjs/helper-api-error/1.13.2:
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==}
    dev: true

  /@webassemblyjs/helper-buffer/1.14.1:
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==}
    dev: true

  /@webassemblyjs/helper-numbers/1.13.2:
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==}
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==}
    dev: true

  /@webassemblyjs/helper-wasm-section/1.14.1:
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1
    dev: true

  /@webassemblyjs/ieee754/1.13.2:
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==}
    dependencies:
      '@xtuc/ieee754': 1.2.0
    dev: true

  /@webassemblyjs/leb128/1.13.2:
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==}
    dependencies:
      '@xtuc/long': 4.2.2
    dev: true

  /@webassemblyjs/utf8/1.13.2:
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==}
    dev: true

  /@webassemblyjs/wasm-edit/1.14.1:
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1
    dev: true

  /@webassemblyjs/wasm-gen/1.14.1:
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2
    dev: true

  /@webassemblyjs/wasm-opt/1.14.1:
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
    dev: true

  /@webassemblyjs/wasm-parser/1.14.1:
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2
    dev: true

  /@webassemblyjs/wast-printer/1.14.1:
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==}
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2
    dev: true

  /@xtuc/ieee754/1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}
    dev: true

  /@xtuc/long/4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}
    dev: true

  /abort-controller/3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}
    dependencies:
      event-target-shim: 5.0.1
    dev: false

  /accepts/1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3
    dev: false

  /acorn-jsx/5.3.2_acorn@8.15.0:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 8.15.0
    dev: true

  /acorn-walk/8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}
    dependencies:
      acorn: 8.15.0
    dev: true

  /acorn/8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  /agentkeepalive/4.6.0:
    resolution: {integrity: sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==}
    engines: {node: '>= 8.0.0'}
    dependencies:
      humanize-ms: 1.2.1
    dev: false

  /ai/4.3.16_react@18.2.0+zod@3.25.70:
    resolution: {integrity: sha512-KUDwlThJ5tr2Vw0A1ZkbDKNME3wzWhuVfAOwIvFUzl1TPVDFAXDFTXio3p+jaKneB+dKNCvFFlolYmmgHttG1g==}
    engines: {node: '>=18'}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      zod: ^3.23.8
    peerDependenciesMeta:
      react:
        optional: true
    dependencies:
      '@ai-sdk/provider': 1.1.3
      '@ai-sdk/provider-utils': 2.2.8_zod@3.25.70
      '@ai-sdk/react': 1.2.12_react@18.2.0+zod@3.25.70
      '@ai-sdk/ui-utils': 1.2.11_zod@3.25.70
      '@opentelemetry/api': 1.9.0
      jsondiffpatch: 0.6.0
      react: 18.2.0
      zod: 3.25.70
    dev: false

  /ajv-formats/2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: 8.17.1
    dev: true

  /ajv-keywords/5.1.0_ajv@8.17.1:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3
    dev: true

  /ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1
    dev: true

  /ajv/8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
    dev: true

  /ansi-colors/4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}
    dev: false

  /ansi-regex/5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: true

  /ansi-regex/6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}
    dev: true

  /ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1

  /ansi-styles/5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}
    dev: false

  /ansi-styles/6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}
    dev: true

  /any-promise/1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}
    dev: true

  /anymatch/3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: true

  /arg/4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==}
    dev: true

  /arg/5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}
    dev: true

  /argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  /aria-hidden/1.2.6:
    resolution: {integrity: sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==}
    engines: {node: '>=10'}
    dependencies:
      tslib: 2.8.1
    dev: false

  /aria-query/5.3.2:
    resolution: {integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==}
    engines: {node: '>= 0.4'}
    dev: true

  /array-buffer-byte-length/1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5
    dev: true

  /array-flatten/1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}
    dev: false

  /array-includes/3.1.9:
    resolution: {integrity: sha512-FmeCCAenzH0KH381SPT5FZmiA/TmpndpcaShhfgEN9eCVjnFBqq3l1xrI42y8+PPLI6hypzou4GXw00WHmPBLQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1
      math-intrinsics: 1.1.0
    dev: true

  /array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /array.prototype.findlast/1.2.5:
    resolution: {integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.findlastindex/1.2.6:
    resolution: {integrity: sha512-F/TKATkzseUExPlfvmwQKGITM3DGTK+vkAsCZoDc5daVygbJBnjEUCbgkAvVFsgfXfX4YIqZ/27G3k3tdXrTxQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.flat/1.3.3:
    resolution: {integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.flatmap/1.3.3:
    resolution: {integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.tosorted/1.1.4:
    resolution: {integrity: sha512-p6Fx8B7b7ZhL/gmUsAy0D15WhvDccw3mnGNbZpi3pmeJdxtWsj2jEaI4Y6oo3XiHfzuSgPwKc04MYt6KgvC/wA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0
    dev: true

  /arraybuffer.prototype.slice/1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5
    dev: true

  /ast-types-flow/0.0.8:
    resolution: {integrity: sha512-OH/2E5Fg20h2aPrbe+QL8JZQFko0YZaF+j4mnQ7BGhfavO7OpSLa8a0y9sBwomHdSbkhTS8TQNayBfnW5DwbvQ==}
    dev: true

  /async-function/1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}
    dev: true

  /asynckit/0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}
    dev: false

  /attr-accept/2.2.5:
    resolution: {integrity: sha512-0bDNnY/u6pPwHDMoF0FieU354oBi0a8rD9FcsLwzcGWbc8KS8KPIi7y+s13OlVY+gMWc/9xEMUgNE6Qm8ZllYQ==}
    engines: {node: '>=4'}
    dev: false

  /available-typed-arrays/1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      possible-typed-array-names: 1.1.0
    dev: true

  /axe-core/4.10.3:
    resolution: {integrity: sha512-Xm7bpRXnDSX2YE2YFfBk2FnF0ep6tmG7xPh8iHee8MIcrgq762Nkce856dYtJYLkuIoYZvGfTs/PbZhideTcEg==}
    engines: {node: '>=4'}
    dev: true

  /axobject-query/4.1.0:
    resolution: {integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==}
    engines: {node: '>= 0.4'}
    dev: true

  /bail/2.0.2:
    resolution: {integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==}
    dev: false

  /balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}
    dev: true

  /base64-js/1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}
    dev: false

  /binary-extensions/2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==}
    engines: {node: '>=8'}
    dev: true

  /body-parser/1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    dev: false

  /body-parser/2.2.0:
    resolution: {integrity: sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==}
    engines: {node: '>=18'}
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 4.4.1
      http-errors: 2.0.0
      iconv-lite: 0.6.3
      on-finished: 2.4.1
      qs: 6.14.0
      raw-body: 3.0.0
      type-is: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /boolbase/1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}
    dev: false

  /brace-expansion/1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1
    dev: true

  /brace-expansion/2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1
    dev: true

  /browserslist/4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: 1.0.30001726
      electron-to-chromium: 1.5.179
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3_browserslist@4.25.1
    dev: true

  /buffer-from/1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: true

  /busboy/1.6.0:
    resolution: {integrity: sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==}
    engines: {node: '>=10.16.0'}
    dependencies:
      streamsearch: 1.1.0
    dev: false

  /bytes/3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}
    dev: false

  /call-bind-apply-helpers/1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  /call-bind/1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2
    dev: true

  /call-bound/1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  /callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /camelcase-css/2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}
    dev: true

  /camelcase/6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}
    dev: false

  /caniuse-lite/1.0.30001726:
    resolution: {integrity: sha512-VQAUIUzBiZ/UnlM28fSp2CRF3ivUn1BWEvxMcVTNwpw91Py1pGbPIyIKtd+tzct9C3ouceCVdGAXxZOpZAsgdw==}

  /ccount/2.0.1:
    resolution: {integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==}
    dev: false

  /chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  /chalk/5.4.1:
    resolution: {integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}
    dev: false

  /character-entities/2.0.2:
    resolution: {integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==}
    dev: false

  /cheerio-select/1.6.0:
    resolution: {integrity: sha512-eq0GdBvxVFbqWgmCm7M3XGs1I8oLy/nExUnh6oLqmBditPO9AqQJrkslDpMun/hZ0yyTs8L0m85OHp4ho6Qm9g==}
    dependencies:
      css-select: 4.3.0
      css-what: 6.2.2
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
    dev: false

  /cheerio/1.0.0-rc.10:
    resolution: {integrity: sha512-g0J0q/O6mW8z5zxQ3A8E8J1hUgp4SMOvEoW/x84OwyHKe/Zccz83PVT4y5Crcr530FV6NgmKI1qvGTKVl9XXVw==}
    engines: {node: '>= 6'}
    dependencies:
      cheerio-select: 1.6.0
      dom-serializer: 1.4.1
      domhandler: 4.3.1
      htmlparser2: 6.1.0
      parse5: 6.0.1
      parse5-htmlparser2-tree-adapter: 6.0.1
      tslib: 2.8.1
    dev: false

  /chokidar/3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /chrome-trace-event/1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}
    dev: true

  /class-variance-authority/0.7.1:
    resolution: {integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==}
    dependencies:
      clsx: 2.1.1
    dev: false

  /client-only/0.0.1:
    resolution: {integrity: sha512-IV3Ou0jSMzZrd3pZ48nLkT9DA7Ag1pnPzaiQhpW7c3RbcqqzvzzVu+L8gfqMp/8IM2MQtSiqaCxrrcfu8I8rMA==}
    dev: false

  /clsx/2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}
    dev: false

  /cmdk/1.1.1_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg==}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      react-dom: ^18 || ^19 || ^19.0.0-rc
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-dialog': 1.1.14_813bbe29c70008681efc49db0ea78d97
      '@radix-ui/react-id': 1.1.1_888dbdc24f3e5b913723df3d4309886e
      '@radix-ui/react-primitive': 2.1.3_813bbe29c70008681efc49db0ea78d97
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
    dev: false

  /color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4

  /color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /commander/2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}
    dev: true

  /commander/4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}
    dev: true

  /commander/6.2.1:
    resolution: {integrity: sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==}
    engines: {node: '>= 6'}
    dev: false

  /commander/8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}
    dev: false

  /compute-scroll-into-view/3.1.1:
    resolution: {integrity: sha512-VRhuHOLoKYOy4UbilLbUzbYg93XLjv2PncJC50EuTWPA3gaja1UjBsUP/D/9/juV3vQFr6XBEzn9KCAHdUvOHw==}
    dev: false

  /concat-map/0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}
    dev: true

  /console-table-printer/2.14.6:
    resolution: {integrity: sha512-MCBl5HNVaFuuHW6FGbL/4fB7N/ormCy+tQ+sxTrF6QtSbSNETvPuOVbkJBhzDgYhvjWGrTma4eYJa37ZuoQsPw==}
    dependencies:
      simple-wcswidth: 1.1.2
    dev: false

  /content-disposition/0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: 5.2.1
    dev: false

  /content-type/1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}
    dev: false

  /cookie-signature/1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}
    dev: false

  /cookie/0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}
    dev: false

  /copy-to-clipboard/3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}
    dependencies:
      toggle-selection: 1.0.6
    dev: false

  /cors/2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2
    dev: false

  /create-require/1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}
    dev: true

  /cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /css-select/4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==}
    dependencies:
      boolbase: 1.0.0
      css-what: 6.2.2
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1
    dev: false

  /css-what/6.2.2:
    resolution: {integrity: sha512-u/O3vwbptzhMs3L1fQE82ZSLHQQfto5gyZzwteVIEyeaY5Fc7R4dapF/BvRoSYFeqfBk4m0V1Vafq5Pjv25wvA==}
    engines: {node: '>= 6'}
    dev: false

  /cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  /d3-array/3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}
    dependencies:
      internmap: 2.0.3
    dev: false

  /d3-color/3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}
    dev: false

  /d3-ease/3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}
    dev: false

  /d3-format/3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}
    dev: false

  /d3-interpolate/3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
    dev: false

  /d3-path/3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}
    dev: false

  /d3-scale/4.0.2:
    resolution: {integrity: sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==}
    engines: {node: '>=12'}
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0
    dev: false

  /d3-shape/3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}
    dependencies:
      d3-path: 3.1.0
    dev: false

  /d3-time-format/4.1.0:
    resolution: {integrity: sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==}
    engines: {node: '>=12'}
    dependencies:
      d3-time: 3.1.0
    dev: false

  /d3-time/3.1.0:
    resolution: {integrity: sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==}
    engines: {node: '>=12'}
    dependencies:
      d3-array: 3.2.4
    dev: false

  /d3-timer/3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}
    dev: false

  /damerau-levenshtein/1.0.8:
    resolution: {integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==}
    dev: true

  /data-view-buffer/1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /data-view-byte-length/1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /data-view-byte-offset/1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /date-fns/3.6.0:
    resolution: {integrity: sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==}
    dev: false

  /debug/2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    dependencies:
      ms: 2.0.0
    dev: false

  /debug/3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    dependencies:
      ms: 2.1.3
    dev: true

  /debug/4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3

  /decamelize/1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /decimal.js-light/2.5.1:
    resolution: {integrity: sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==}
    dev: false

  /decode-named-character-reference/1.2.0:
    resolution: {integrity: sha512-c6fcElNV6ShtZXmsgNgFFV5tVX2PaV4g+MOAkb8eXHvn6sryJBrZa9r0zV6+dtTyoCKxtDy5tyQ5ZwQuidtd+Q==}
    dependencies:
      character-entities: 2.0.2
    dev: false

  /deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    dev: true

  /define-data-property/1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0
    dev: true

  /define-properties/1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1
    dev: true

  /delayed-stream/1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}
    dev: false

  /depd/2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}
    dev: false

  /dequal/2.0.3:
    resolution: {integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==}
    engines: {node: '>=6'}
    dev: false

  /destroy/1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: false

  /detect-libc/2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}
    dev: false
    optional: true

  /detect-node-es/1.1.0:
    resolution: {integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==}
    dev: false

  /devlop/1.1.0:
    resolution: {integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==}
    dependencies:
      dequal: 2.0.3
    dev: false

  /didyoumean/1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}
    dev: true

  /diff-match-patch-ts/0.6.0:
    resolution: {integrity: sha512-U0uPIJ+wJqgaBoVw2MFSFpGIk7q3mJJ+/sehbxDZFv4Gx6a1GOmrsSLmxVDDrGtRL4Q9de084aa5lVpCHn+eUw==}
    dev: false

  /diff-match-patch/1.0.5:
    resolution: {integrity: sha512-IayShXAgj/QMXgB0IWmKx+rOPuGMhqm5w6jvFxmVenXKIzRqTAAsbBPT3kWQeGANj3jGgvcvv4yK6SxqYmikgw==}
    dev: false

  /diff/4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}
    dev: true

  /dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /direction/1.0.4:
    resolution: {integrity: sha512-GYqKi1aH7PJXxdhTeZBFrg8vUBeKXi+cNprXsC1kpJcbcVnV9wBsrOu1cQEdG0WeQwlfHiy3XvnKfIrJ2R0NzQ==}
    hasBin: true
    dev: false

  /dlv/1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}
    dev: true

  /dnd-core/16.0.1:
    resolution: {integrity: sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng==}
    dependencies:
      '@react-dnd/asap': 5.0.2
      '@react-dnd/invariant': 4.0.2
      redux: 4.2.1
    dev: false

  /doctrine/2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine/3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-helpers/5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==}
    dependencies:
      '@babel/runtime': 7.27.6
      csstype: 3.1.3
    dev: false

  /dom-serializer/1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0
    dev: false

  /domelementtype/2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}
    dev: false

  /domhandler/3.3.0:
    resolution: {integrity: sha512-J1C5rIANUbuYK+FuFL98650rihynUOEzRLxW+90bKZRWB6A1X1Tf82GxR1qAWLyfNPRvjqfip3Q5tdYlmAa9lA==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: false

  /domhandler/4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==}
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: 2.3.0
    dev: false

  /domutils/2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==}
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1
    dev: false

  /dunder-proto/1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  /eastasianwidth/0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}
    dev: true

  /ee-first/1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}
    dev: false

  /effect/3.16.8:
    resolution: {integrity: sha512-E4U0MZFBun99myxOogy9ZZ1c3IYR47L/A5GqCP9Lp+6ORag0YLmGHOrYxQ3agN1FOMTrElgtJmciicwnHdE+Ug==}
    dependencies:
      '@standard-schema/spec': 1.0.0
      fast-check: 3.23.2
    dev: false

  /electron-to-chromium/1.5.179:
    resolution: {integrity: sha512-UWKi/EbBopgfFsc5k61wFpV7WrnnSlSzW/e2XcBmS6qKYTivZlLtoll5/rdqRTxGglGHkmkW0j0pFNJG10EUIQ==}
    dev: true

  /embla-carousel-react/8.6.0_react@18.2.0:
    resolution: {integrity: sha512-0/PjqU7geVmo6F734pmPqpyHqiM99olvyecY7zdweCw+6tKEXnrE90pBiBbMMU8s5tICemzpQ3hi5EpxzGW+JA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      embla-carousel: 8.6.0
      embla-carousel-reactive-utils: 8.6.0_embla-carousel@8.6.0
      react: 18.2.0
    dev: false

  /embla-carousel-reactive-utils/8.6.0_embla-carousel@8.6.0:
    resolution: {integrity: sha512-fMVUDUEx0/uIEDM0Mz3dHznDhfX+znCCDCeIophYb1QGVM7YThSWX+wz11zlYwWFOr74b4QLGg0hrGPJeG2s4A==}
    peerDependencies:
      embla-carousel: 8.6.0
    dependencies:
      embla-carousel: 8.6.0
    dev: false

  /embla-carousel/8.6.0:
    resolution: {integrity: sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==}
    dev: false

  /emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /emoji-regex/9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}
    dev: true

  /encodeurl/1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}
    dev: false

  /encodeurl/2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}
    dev: false

  /enhanced-resolve/5.18.2:
    resolution: {integrity: sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==}
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2
    dev: true

  /entities/2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==}
    dev: false

  /entities/4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}
    dev: false

  /es-abstract/1.24.0:
    resolution: {integrity: sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-negative-zero: 2.0.3
      is-regex: 1.2.1
      is-set: 2.0.3
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      stop-iteration-iterator: 1.1.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19
    dev: true

  /es-define-property/1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  /es-errors/1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  /es-iterator-helpers/1.2.1:
    resolution: {integrity: sha512-uDn+FE1yrDzyC0pCo961B2IHbdM8y/ACZsKD4dG6WqrjV53BADjwa7D+1aom2rsNVfLyDgU/eigvlJGJ08OQ4w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3
    dev: true

  /es-module-lexer/1.7.0:
    resolution: {integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==}
    dev: true

  /es-object-atoms/1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0

  /es-set-tostringtag/2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  /es-shim-unscopables/1.1.0:
    resolution: {integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /es-to-primitive/1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1
    dev: true

  /escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}
    dev: true

  /escape-goat/3.0.0:
    resolution: {integrity: sha512-w3PwNZJwRxlp47QGzhuEBldEqVHHhh8/tIPcl6ecf2Bou99cdAt0knihBV0Ecc7CGxYduXVBDheH1K2oADRlvw==}
    engines: {node: '>=10'}
    dev: false

  /escape-html/1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}
    dev: false

  /escape-string-regexp/4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}
    dev: true

  /escape-string-regexp/5.0.0:
    resolution: {integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==}
    engines: {node: '>=12'}
    dev: false

  /eslint-config-next/14.2.30_eslint@8.57.1+typescript@5.8.3:
    resolution: {integrity: sha512-4pTMb3wfpI+piVeEz3TWG1spjuXJJBZaYabi2H08z2ZTk6/N304POEovHdFmK6EZb4QlKpETulBNaRIITA0+xg==}
    peerDependencies:
      eslint: ^7.23.0 || ^8.0.0
      typescript: '>=3.3.1'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@next/eslint-plugin-next': 14.2.30
      '@rushstack/eslint-patch': 1.12.0
      '@typescript-eslint/eslint-plugin': 7.18.0_ec52852665279724a7bf6969b667668f
      '@typescript-eslint/parser': 7.18.0_eslint@8.57.1+typescript@5.8.3
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-import-resolver-typescript: 3.10.1_d12f610e8210c918229aa7e16e445f1f
      eslint-plugin-import: 2.32.0_eslint@8.57.1
      eslint-plugin-jsx-a11y: 6.10.2_eslint@8.57.1
      eslint-plugin-react: 7.37.5_eslint@8.57.1
      eslint-plugin-react-hooks: 5.0.0-canary-7118f5dd7-20230705_eslint@8.57.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - eslint-plugin-import-x
      - supports-color
    dev: true

  /eslint-import-resolver-node/0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    dev: true

  /eslint-import-resolver-typescript/3.10.1_d12f610e8210c918229aa7e16e445f1f:
    resolution: {integrity: sha512-A1rHYb06zjMGAxdLSkN2fXPBwuSaQ0iO5M/hdyS0Ajj1VBaRp0sPD3dn1FhME3c/JluGFbwSxyCfqdSbtQLAHQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      eslint-plugin-import: '*'
      eslint-plugin-import-x: '*'
    peerDependenciesMeta:
      eslint-plugin-import:
        optional: true
      eslint-plugin-import-x:
        optional: true
    dependencies:
      '@nolyfill/is-core-module': 1.0.39
      debug: 4.4.1
      eslint: 8.57.1
      eslint-plugin-import: 2.32.0_eslint@8.57.1
      get-tsconfig: 4.10.1
      is-bun-module: 2.0.0
      stable-hash: 0.0.5
      tinyglobby: 0.2.14
      unrs-resolver: 1.10.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-module-utils/2.12.1_eslint@8.57.1:
    resolution: {integrity: sha512-L8jSWTze7K2mTg0vos/RuLRS5soomksDPoJLXIslC7c8Wmut3bx7CPpJijDcBZtxQ5lrbUdM+s0OlNbz0DCDNw==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: '*'
    peerDependenciesMeta:
      eslint:
        optional: true
    dependencies:
      debug: 3.2.7
      eslint: 8.57.1
    dev: true

  /eslint-plugin-import/2.32.0_eslint@8.57.1:
    resolution: {integrity: sha512-whOE1HFo/qJDyX4SnXzP4N6zOWn79WhnCUY/iDR0mPfQZO8wcYE4JClzI2oZrhBnnMUCBCHZhO6VQyoBU95mZA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    dependencies:
      '@rtsao/scc': 1.1.0
      array-includes: 3.1.9
      array.prototype.findlastindex: 1.2.6
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.1
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.1_eslint@8.57.1
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    dev: true

  /eslint-plugin-jsx-a11y/6.10.2_eslint@8.57.1:
    resolution: {integrity: sha512-scB3nz4WmG75pV8+3eRUQOHZlNSUhFNq37xnpgRkCCELU3XMvXAxLk1eqWWyE22Ki4Q01Fnsw9BA3cJHDPgn2Q==}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
    dependencies:
      aria-query: 5.3.2
      array-includes: 3.1.9
      array.prototype.flatmap: 1.3.3
      ast-types-flow: 0.0.8
      axe-core: 4.10.3
      axobject-query: 4.1.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 9.2.2
      eslint: 8.57.1
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      language-tags: 1.0.9
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      safe-regex-test: 1.1.0
      string.prototype.includes: 2.0.1
    dev: true

  /eslint-plugin-react-hooks/5.0.0-canary-7118f5dd7-20230705_eslint@8.57.1:
    resolution: {integrity: sha512-AZYbMo/NW9chdL7vk6HQzQhT+PvTAEVqWk9ziruUoW2kAOcN5qNyelv70e0F1VNQAbvutOC9oc+xfWycI9FxDw==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
    dependencies:
      eslint: 8.57.1
    dev: true

  /eslint-plugin-react/7.37.5_eslint@8.57.1:
    resolution: {integrity: sha512-Qteup0SqU15kdocexFNAJMvCJEfa2xUKNV4CC1xsVMrIIqEy3SQ/rqyxCWNzfrd3/ldy6HMlD2e0JDVpDg2qIA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
    dependencies:
      array-includes: 3.1.9
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 8.57.1
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0
    dev: true

  /eslint-scope/5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: true

  /eslint-scope/7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0
    dev: true

  /eslint-visitor-keys/3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint/8.57.1:
    resolution: {integrity: sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0_eslint@8.57.1
      '@eslint-community/regexpp': 4.12.1
      '@eslint/eslintrc': 2.1.4
      '@eslint/js': 8.57.1
      '@humanwhocodes/config-array': 0.13.0
      '@humanwhocodes/module-importer': 1.0.1
      '@nodelib/fs.walk': 1.2.8
      '@ungap/structured-clone': 1.3.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree/9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2_acorn@8.15.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /esquery/1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse/4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: true

  /estraverse/5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: true

  /esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /etag/1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}
    dev: false

  /event-target-shim/5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}
    dev: false

  /eventemitter3/4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: false

  /events/3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}
    dev: true

  /eventsource-parser/1.1.2:
    resolution: {integrity: sha512-v0eOBUbiaFojBu2s2NPBfYUoRR9GjcDNvCXVaqEf5vVfpIAh9f8RCo4vXTP8c63QRKCFwoLpMpTdPwwhEKVgzA==}
    engines: {node: '>=14.18'}
    dev: false

  /express/4.21.2:
    resolution: {integrity: sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==}
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    dev: false

  /extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}
    dev: false

  /fast-check/3.23.2:
    resolution: {integrity: sha512-h5+1OzzfCC3Ef7VbtKdcv7zsstUQwUDlYpUTvjeUsJAssPgLn7QzbboPtL5ro04Mq0rPOsMzl7q5hIbRs2wD1A==}
    engines: {node: '>=8.0.0'}
    dependencies:
      pure-rand: 6.1.0
    dev: false

  /fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  /fast-equals/5.2.2:
    resolution: {integrity: sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==}
    engines: {node: '>=6.0.0'}
    dev: false

  /fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: true

  /fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}
    dev: true

  /fast-levenshtein/2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}
    dev: true

  /fast-uri/3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}
    dev: true

  /fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}
    dependencies:
      reusify: 1.1.0
    dev: true

  /fdir/6.4.6_picomatch@4.0.2:
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true
    dependencies:
      picomatch: 4.0.2
    dev: true

  /file-entry-cache/6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: 3.2.0
    dev: true

  /file-selector/0.6.0:
    resolution: {integrity: sha512-QlZ5yJC0VxHxQQsQhXvBaC7VRJ2uaxTf+Tfpu4Z/OcVQJVpZO+DGU0rkoVW5ce2SccxugvpBJoMvUs59iILYdw==}
    engines: {node: '>= 12'}
    dependencies:
      tslib: 2.8.1
    dev: false

  /file-selector/2.1.2:
    resolution: {integrity: sha512-QgXo+mXTe8ljeqUFaX3QVHc5osSItJ/Km+xpocx0aSqWGMSCf6qYs/VnzZgS864Pjn5iceMRFigeAV7AfTlaig==}
    engines: {node: '>= 12'}
    dependencies:
      tslib: 2.8.1
    dev: false

  /fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /finalhandler/1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    dev: false

  /find-my-way-ts/0.1.5:
    resolution: {integrity: sha512-4GOTMrpGQVzsCH2ruUn2vmwzV/02zF4q+ybhCIrw/Rkt3L8KWcycdC6aJMctJzwN4fXD4SD5F/4B9Sksh5rE0A==}
    dev: false

  /find-up/5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /flat-cache/3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
      rimraf: 3.0.2
    dev: true

  /flatted/3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}
    dev: true

  /for-each/0.3.5:
    resolution: {integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
    dev: true

  /foreground-child/3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0
    dev: true

  /form-data-encoder/1.7.2:
    resolution: {integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==}
    dev: false

  /form-data/4.0.3:
    resolution: {integrity: sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35
    dev: false

  /formdata-node/4.4.1:
    resolution: {integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==}
    engines: {node: '>= 12.20'}
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3
    dev: false

  /forwarded/0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}
    dev: false

  /fresh/0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}
    dev: false

  /fs.realpath/1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}
    dev: true

  /fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    optional: true

  /function-bind/1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  /function.prototype.name/1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7
    dev: true

  /functions-have-names/1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}
    dev: true

  /get-intrinsic/1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  /get-nonce/1.0.1:
    resolution: {integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==}
    engines: {node: '>=6'}
    dev: false

  /get-proto/1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  /get-symbol-description/1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
    dev: true

  /get-tsconfig/4.10.1:
    resolution: {integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==}
    dependencies:
      resolve-pkg-maps: 1.0.0
    dev: true

  /glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-to-regexp/0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}
    dev: true

  /glob/10.3.10:
    resolution: {integrity: sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 2.3.6
      minimatch: 9.0.5
      minipass: 7.1.2
      path-scurry: 1.11.1
    dev: true

  /glob/10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1
    dev: true

  /glob/7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1
    dev: true

  /globals/13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.20.2
    dev: true

  /globalthis/1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0
    dev: true

  /globby/11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /gopd/1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  /graceful-fs/4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  /graphemer/1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}
    dev: true

  /has-bigints/1.1.0:
    resolution: {integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==}
    engines: {node: '>= 0.4'}
    dev: true

  /has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  /has-property-descriptors/1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.1
    dev: true

  /has-proto/1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
    dev: true

  /has-symbols/1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  /has-tostringtag/1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0

  /hasown/2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /hoist-non-react-statics/3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1
    dev: false

  /html-entities/2.6.0:
    resolution: {integrity: sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==}
    dev: false

  /htmlparser2/5.0.1:
    resolution: {integrity: sha512-vKZZra6CSe9qsJzh0BjBGXo8dvzNsq/oGvsjfRdOrrryfeD9UOBEEQdeoqCRmKZchF5h2zOBMQ6YuQ0uRUmdbQ==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 3.3.0
      domutils: 2.8.0
      entities: 2.2.0
    dev: false

  /htmlparser2/6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==}
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0
    dev: false

  /http-errors/2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1
    dev: false

  /humanize-ms/1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}
    dependencies:
      ms: 2.1.3
    dev: false

  /iconv-lite/0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /iconv-lite/0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /ignore/5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}
    dev: true

  /immer/10.1.1:
    resolution: {integrity: sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw==}
    dev: false

  /import-fresh/3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /imurmurhash/0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}
    dev: true

  /inflight/1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2
    dev: true

  /inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /input-otp/1.4.2_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-l3jWwYNvrEa6NTCt7BECfCm48GvwuZzkoeG3gBL2w4CHeOXW3eKFmf9UNYkNfYc3mxMrthMnxjIE07MT0zLBQA==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /internal-slot/1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0
    dev: true

  /internmap/2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}
    dev: false

  /ipaddr.js/1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}
    dev: false

  /is-array-buffer/3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
    dev: true

  /is-async-function/2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0
    dev: true

  /is-bigint/1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-bigints: 1.1.0
    dev: true

  /is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.3.0
    dev: true

  /is-boolean-object/1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-bun-module/2.0.0:
    resolution: {integrity: sha512-gNCGbnnnnFAUGKeZ9PdbyeGYJqewpmc2aKHUEMO5nQPWU9lOmv7jcmQIv+qHD8fXW6W7qfuCwX4rY9LNRjXrkQ==}
    dependencies:
      semver: 7.7.2
    dev: true

  /is-callable/1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-core-module/2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /is-data-view/1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15
    dev: true

  /is-date-object/1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-finalizationregistry/1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
    dev: true

  /is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-generator-function/1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0
    dev: true

  /is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1
    dev: true

  /is-hotkey/0.2.0:
    resolution: {integrity: sha512-UknnZK4RakDmTgz4PI1wIph5yxSs/mvChWs9ifnlXsKuXgWmOkY/hAE0H/k2MIqH0RlRye0i1oC07MCRSD28Mw==}
    dev: false

  /is-map/2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-negative-zero/2.0.3:
    resolution: {integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-number-object/1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}
    dev: true

  /is-path-inside/3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==}
    engines: {node: '>=8'}
    dev: true

  /is-plain-obj/4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==}
    engines: {node: '>=12'}
    dev: false

  /is-plain-object/5.0.0:
    resolution: {integrity: sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-regex/1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: true

  /is-set/2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-shared-array-buffer/1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
    dev: true

  /is-string/1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2
    dev: true

  /is-symbol/1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0
    dev: true

  /is-typed-array/1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.19
    dev: true

  /is-weakmap/2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-weakref/1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
    dev: true

  /is-weakset/2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
    dev: true

  /isarray/2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}
    dev: true

  /isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}
    dev: true

  /iterator.prototype/1.1.5:
    resolution: {integrity: sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2
    dev: true

  /jackspeak/2.3.6:
    resolution: {integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==}
    engines: {node: '>=14'}
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0
    dev: true

  /jackspeak/3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0
    dev: true

  /jest-worker/27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': 20.19.4
      merge-stream: 2.0.0
      supports-color: 8.1.1
    dev: true

  /jiti/1.21.7:
    resolution: {integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==}
    hasBin: true
    dev: true

  /jotai-optics/0.4.0_jotai@2.8.4+optics-ts@2.4.1:
    resolution: {integrity: sha512-osbEt9AgS55hC4YTZDew2urXKZkaiLmLqkTS/wfW5/l0ib8bmmQ7kBXSFaosV6jDDWSp00IipITcJARFHdp42g==}
    peerDependencies:
      jotai: '>=2.0.0'
      optics-ts: '>=2.0.0'
    dependencies:
      jotai: 2.8.4_888dbdc24f3e5b913723df3d4309886e
      optics-ts: 2.4.1
    dev: false

  /jotai-x/1.2.4_5d95b62253ad84e67439488ad15d42b3:
    resolution: {integrity: sha512-FyLrAR/ZDtmaWgif4cNRuJvMam/RSFv+B11/p4T427ws/T+8WhZzwmULwNogG6ZbZq+v1XpH6f9aN1lYqY5dLg==}
    peerDependencies:
      '@types/react': '>=17.0.0'
      jotai: '>=2.0.0'
      react: '>=17.0.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 18.3.23
      jotai: 2.8.4_888dbdc24f3e5b913723df3d4309886e
      react: 18.2.0
    dev: false

  /jotai/2.8.4_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-f6jwjhBJcDtpeauT2xH01gnqadKEySwwt1qNBLvAXcnojkmb76EdqRt05Ym8IamfHGAQz2qMKAwftnyjeSoHAA==}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=17.0.0'
      react: '>=17.0.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /js-tiktoken/1.0.20:
    resolution: {integrity: sha512-Xlaqhhs8VfCd6Sh7a1cFkZHQbYTLCwVJJWiHVxBYzLPxW0XsoxBy1hitmjkdIjD3Aon5BXLHFwU5O8WUx6HH+A==}
    dependencies:
      base64-js: 1.5.1
    dev: false

  /js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-video-url-parser/0.5.1:
    resolution: {integrity: sha512-/vwqT67k0AyIGMHAvSOt+n4JfrZWF7cPKgKswDO35yr27GfW4HtjpQVlTx6JLF45QuPm8mkzFHkZgFVnFm4x/w==}
    dev: false

  /js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1

  /json-buffer/3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}
    dev: true

  /json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: true

  /json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}
    dev: true

  /json-schema-traverse/1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}
    dev: true

  /json-schema/0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==}
    dev: false

  /json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}
    dev: true

  /json5/1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /jsondiffpatch/0.6.0:
    resolution: {integrity: sha512-3QItJOXp2AP1uv7waBkao5nCvhEv+QmJAd38Ybq7wNI74Q+BBmnLn4EDKz6yI9xGAIQoUF87qHt+kc1IVxB4zQ==}
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    dependencies:
      '@types/diff-match-patch': 1.0.36
      chalk: 5.4.1
      diff-match-patch: 1.0.5
    dev: false

  /jsonpointer/5.0.1:
    resolution: {integrity: sha512-p/nXbhSEcu3pZRdkW1OfJhpsVtW1gd4Wa1fnQc9YLiTfAjn0312eMKimbdIQzuZl9aa9xUGaRlP9T/CJE/ditQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /jsx-ast-utils/3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}
    dependencies:
      array-includes: 3.1.9
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1
    dev: true

  /juice/8.1.0:
    resolution: {integrity: sha512-FLzurJrx5Iv1e7CfBSZH68dC04EEvXvvVvPYB7Vx1WAuhCp1ZPIMtqxc+WTWxVkpTIC2Ach/GAv0rQbtGf6YMA==}
    engines: {node: '>=10.0.0'}
    hasBin: true
    dependencies:
      cheerio: 1.0.0-rc.10
      commander: 6.2.1
      mensch: 0.3.4
      slick: 1.12.2
      web-resource-inliner: 6.0.1
    transitivePeerDependencies:
      - encoding
    dev: false

  /katex/0.16.11:
    resolution: {integrity: sha512-RQrI8rlHY92OLf3rho/Ts8i/XvjgguEjOkO1BEXcU3N8BqPpSzBNwV/G0Ukr+P/l3ivvJUE/Fa/CwbS6HesGNQ==}
    hasBin: true
    dependencies:
      commander: 8.3.0
    dev: false

  /keyv/4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}
    dependencies:
      json-buffer: 3.0.1
    dev: true

  /langchain/0.3.29_@langchain+core@0.3.36:
    resolution: {integrity: sha512-L389pKlApVJPqu4hp58qY6NZAobI+MFPoBjSfjT1z3mcxtB68wLFGhaH4DVsTVg21NYO+0wTEoz24BWrxu9YGw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@langchain/anthropic': '*'
      '@langchain/aws': '*'
      '@langchain/cerebras': '*'
      '@langchain/cohere': '*'
      '@langchain/core': '>=0.3.58 <0.4.0'
      '@langchain/deepseek': '*'
      '@langchain/google-genai': '*'
      '@langchain/google-vertexai': '*'
      '@langchain/google-vertexai-web': '*'
      '@langchain/groq': '*'
      '@langchain/mistralai': '*'
      '@langchain/ollama': '*'
      '@langchain/xai': '*'
      axios: '*'
      cheerio: '*'
      handlebars: ^4.7.8
      peggy: ^3.0.2
      typeorm: '*'
    peerDependenciesMeta:
      '@langchain/anthropic':
        optional: true
      '@langchain/aws':
        optional: true
      '@langchain/cerebras':
        optional: true
      '@langchain/cohere':
        optional: true
      '@langchain/deepseek':
        optional: true
      '@langchain/google-genai':
        optional: true
      '@langchain/google-vertexai':
        optional: true
      '@langchain/google-vertexai-web':
        optional: true
      '@langchain/groq':
        optional: true
      '@langchain/mistralai':
        optional: true
      '@langchain/ollama':
        optional: true
      '@langchain/xai':
        optional: true
      axios:
        optional: true
      cheerio:
        optional: true
      handlebars:
        optional: true
      peggy:
        optional: true
      typeorm:
        optional: true
    dependencies:
      '@langchain/core': 0.3.36
      '@langchain/openai': 0.4.9_@langchain+core@0.3.36
      '@langchain/textsplitters': 0.1.0_@langchain+core@0.3.36
      js-tiktoken: 1.0.20
      js-yaml: 4.1.0
      jsonpointer: 5.0.1
      langsmith: 0.3.37
      openapi-types: 12.1.3
      p-retry: 4.6.2
      uuid: 10.0.0
      yaml: 2.8.0
      zod: 3.25.70
    transitivePeerDependencies:
      - '@opentelemetry/api'
      - '@opentelemetry/exporter-trace-otlp-proto'
      - '@opentelemetry/sdk-trace-base'
      - encoding
      - openai
      - ws
    dev: false

  /langsmith/0.3.37:
    resolution: {integrity: sha512-aDFM+LbT01gP8hsJNs4QJjmbRNfoifqhpCSpk8j4k/V8wejEgvgATbgj9W9DQsfQFdtfwx+8G48sK5/0PqQisg==}
    peerDependencies:
      '@opentelemetry/api': '*'
      '@opentelemetry/exporter-trace-otlp-proto': '*'
      '@opentelemetry/sdk-trace-base': '*'
      openai: '*'
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@opentelemetry/exporter-trace-otlp-proto':
        optional: true
      '@opentelemetry/sdk-trace-base':
        optional: true
      openai:
        optional: true
    dependencies:
      '@types/uuid': 10.0.0
      chalk: 4.1.2
      console-table-printer: 2.14.6
      p-queue: 6.6.2
      p-retry: 4.6.2
      semver: 7.7.2
      uuid: 10.0.0
    dev: false

  /language-subtag-registry/0.3.23:
    resolution: {integrity: sha512-0K65Lea881pHotoGEa5gDlMxt3pctLi2RplBb7Ezh4rRdLEOtgi7n4EwK9lamnUCkKBqaeKRVebTq6BAxSkpXQ==}
    dev: true

  /language-tags/1.0.9:
    resolution: {integrity: sha512-MbjN408fEndfiQXbFQ1vnd+1NoLDsnQW41410oQBXiyXDMYH5z505juWa4KUE1LqxRC7DgOgZDbKLxHIwm27hA==}
    engines: {node: '>=0.10'}
    dependencies:
      language-subtag-registry: 0.3.23
    dev: true

  /levn/0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0
    dev: true

  /lilconfig/3.1.3:
    resolution: {integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==}
    engines: {node: '>=14'}
    dev: true

  /lines-and-columns/1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}
    dev: true

  /linkify-it/5.0.0:
    resolution: {integrity: sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==}
    dependencies:
      uc.micro: 2.1.0
    dev: false

  /loader-runner/4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}
    dev: true

  /locate-path/6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash.castarray/4.4.0:
    resolution: {integrity: sha512-aVx8ztPv7/2ULbArGJ2Y42bG1mEQ5mGjpdvrbJcJFU3TbYybe+QlLS4pst9zV52ymy2in1KpFPiZnAOATxD4+Q==}
    dev: true

  /lodash.debounce/4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}
    dev: false

  /lodash.isplainobject/4.0.6:
    resolution: {integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==}
    dev: true

  /lodash.mapvalues/4.6.0:
    resolution: {integrity: sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ==}
    dev: false

  /lodash.merge/4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}
    dev: true

  /lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /longest-streak/3.1.0:
    resolution: {integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==}
    dev: false

  /loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0

  /lru-cache/10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}
    dev: true

  /lucide-react/0.379.0_react@18.2.0:
    resolution: {integrity: sha512-KcdeVPqmhRldldAAgptb8FjIunM2x2Zy26ZBh1RsEUcdLIvsEmbcw7KpzFYUy5BbpGeWhPu9Z9J5YXfStiXwhg==}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /make-error/1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}
    dev: true

  /markdown-it/14.1.0:
    resolution: {integrity: sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
      entities: 4.5.0
      linkify-it: 5.0.0
      mdurl: 2.0.0
      punycode.js: 2.3.1
      uc.micro: 2.1.0
    dev: false

  /markdown-table/3.0.4:
    resolution: {integrity: sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==}
    dev: false

  /math-intrinsics/1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  /mdast-util-find-and-replace/3.0.2:
    resolution: {integrity: sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==}
    dependencies:
      '@types/mdast': 4.0.4
      escape-string-regexp: 5.0.0
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1
    dev: false

  /mdast-util-from-markdown/2.0.2:
    resolution: {integrity: sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==}
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.2
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-decode-string: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-autolink-literal/2.0.1:
    resolution: {integrity: sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==}
    dependencies:
      '@types/mdast': 4.0.4
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-find-and-replace: 3.0.2
      micromark-util-character: 2.1.1
    dev: false

  /mdast-util-gfm-footnote/2.1.0:
    resolution: {integrity: sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==}
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      micromark-util-normalize-identifier: 2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-strikethrough/2.0.0:
    resolution: {integrity: sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==}
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-table/2.0.0:
    resolution: {integrity: sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==}
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      markdown-table: 3.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm-task-list-item/2.0.0:
    resolution: {integrity: sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==}
    dependencies:
      '@types/mdast': 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-gfm/3.1.0:
    resolution: {integrity: sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==}
    dependencies:
      mdast-util-from-markdown: 2.0.2
      mdast-util-gfm-autolink-literal: 2.0.1
      mdast-util-gfm-footnote: 2.1.0
      mdast-util-gfm-strikethrough: 2.0.0
      mdast-util-gfm-table: 2.0.0
      mdast-util-gfm-task-list-item: 2.0.0
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /mdast-util-phrasing/4.1.0:
    resolution: {integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==}
    dependencies:
      '@types/mdast': 4.0.4
      unist-util-is: 6.0.0
    dev: false

  /mdast-util-to-markdown/2.1.2:
    resolution: {integrity: sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==}
    dependencies:
      '@types/mdast': 4.0.4
      '@types/unist': 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-classify-character: 2.0.1
      micromark-util-decode-string: 2.0.1
      unist-util-visit: 5.0.0
      zwitch: 2.0.4
    dev: false

  /mdast-util-to-string/4.0.0:
    resolution: {integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==}
    dependencies:
      '@types/mdast': 4.0.4
    dev: false

  /mdurl/2.0.0:
    resolution: {integrity: sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==}
    dev: false

  /media-typer/0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}
    dev: false

  /media-typer/1.1.0:
    resolution: {integrity: sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==}
    engines: {node: '>= 0.8'}
    dev: false

  /mensch/0.3.4:
    resolution: {integrity: sha512-IAeFvcOnV9V0Yk+bFhYR07O3yNina9ANIN5MoXBKYJ/RLYPurd2d0yw14MDhpr9/momp0WofT1bPUh3hkzdi/g==}
    dev: false

  /merge-descriptors/1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}
    dev: false

  /merge-stream/2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: true

  /methods/1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}
    dev: false

  /micromark-core-commonmark/2.0.3:
    resolution: {integrity: sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==}
    dependencies:
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      micromark-factory-destination: 2.0.1
      micromark-factory-label: 2.0.1
      micromark-factory-space: 2.0.1
      micromark-factory-title: 2.0.1
      micromark-factory-whitespace: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-html-tag-name: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm-autolink-literal/2.1.0:
    resolution: {integrity: sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==}
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm-footnote/2.1.0:
    resolution: {integrity: sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==}
    dependencies:
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm-strikethrough/2.1.0:
    resolution: {integrity: sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==}
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm-table/2.1.1:
    resolution: {integrity: sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==}
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm-tagfilter/2.0.0:
    resolution: {integrity: sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==}
    dependencies:
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm-task-list-item/2.1.0:
    resolution: {integrity: sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==}
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-extension-gfm/3.0.0:
    resolution: {integrity: sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==}
    dependencies:
      micromark-extension-gfm-autolink-literal: 2.1.0
      micromark-extension-gfm-footnote: 2.1.0
      micromark-extension-gfm-strikethrough: 2.1.0
      micromark-extension-gfm-table: 2.1.1
      micromark-extension-gfm-tagfilter: 2.0.0
      micromark-extension-gfm-task-list-item: 2.1.0
      micromark-util-combine-extensions: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-factory-destination/2.0.1:
    resolution: {integrity: sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==}
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-factory-label/2.0.1:
    resolution: {integrity: sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==}
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-factory-space/2.0.1:
    resolution: {integrity: sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==}
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-factory-title/2.0.1:
    resolution: {integrity: sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==}
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-factory-whitespace/2.0.1:
    resolution: {integrity: sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==}
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-util-character/2.1.1:
    resolution: {integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==}
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-util-chunked/2.0.1:
    resolution: {integrity: sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==}
    dependencies:
      micromark-util-symbol: 2.0.1
    dev: false

  /micromark-util-classify-character/2.0.1:
    resolution: {integrity: sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==}
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-util-combine-extensions/2.0.1:
    resolution: {integrity: sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==}
    dependencies:
      micromark-util-chunked: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-util-decode-numeric-character-reference/2.0.2:
    resolution: {integrity: sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==}
    dependencies:
      micromark-util-symbol: 2.0.1
    dev: false

  /micromark-util-decode-string/2.0.1:
    resolution: {integrity: sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==}
    dependencies:
      decode-named-character-reference: 1.2.0
      micromark-util-character: 2.1.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-symbol: 2.0.1
    dev: false

  /micromark-util-encode/2.0.1:
    resolution: {integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==}
    dev: false

  /micromark-util-html-tag-name/2.0.1:
    resolution: {integrity: sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==}
    dev: false

  /micromark-util-normalize-identifier/2.0.1:
    resolution: {integrity: sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==}
    dependencies:
      micromark-util-symbol: 2.0.1
    dev: false

  /micromark-util-resolve-all/2.0.1:
    resolution: {integrity: sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==}
    dependencies:
      micromark-util-types: 2.0.2
    dev: false

  /micromark-util-sanitize-uri/2.0.1:
    resolution: {integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==}
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1
    dev: false

  /micromark-util-subtokenize/2.1.0:
    resolution: {integrity: sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==}
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    dev: false

  /micromark-util-symbol/2.0.1:
    resolution: {integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==}
    dev: false

  /micromark-util-types/2.0.2:
    resolution: {integrity: sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==}
    dev: false

  /micromark/4.0.2:
    resolution: {integrity: sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==}
    dependencies:
      '@types/debug': 4.1.12
      debug: 4.4.1
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-combine-extensions: 2.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-encode: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    transitivePeerDependencies:
      - supports-color
    dev: false

  /micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: true

  /mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  /mime-db/1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0

  /mime-types/3.0.1:
    resolution: {integrity: sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.54.0
    dev: false

  /mime/1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: false

  /mime/2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==}
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: false

  /minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.12
    dev: true

  /minimatch/9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.2
    dev: true

  /minimist/1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}
    dev: true

  /minipass/7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}
    dev: true

  /ms/2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}
    dev: false

  /ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  /msgpackr-extract/3.0.3:
    resolution: {integrity: sha512-P0efT1C9jIdVRefqjzOQ9Xml57zpOXnIuS+csaB4MdZbTdmGDLo8XhzBG1N7aO11gKDDkJvBLULeFTo46wwreA==}
    hasBin: true
    requiresBuild: true
    dependencies:
      node-gyp-build-optional-packages: 5.2.2
    optionalDependencies:
      '@msgpackr-extract/msgpackr-extract-darwin-arm64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-darwin-x64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-linux-arm': 3.0.3
      '@msgpackr-extract/msgpackr-extract-linux-arm64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-linux-x64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-win32-x64': 3.0.3
    dev: false
    optional: true

  /msgpackr/1.11.4:
    resolution: {integrity: sha512-uaff7RG9VIC4jacFW9xzL3jc0iM32DNHe4jYVycBcjUePT/Klnfj7pqtWJt9khvDFizmjN2TlYniYmSS2LIaZg==}
    optionalDependencies:
      msgpackr-extract: 3.0.3
    dev: false

  /multipasta/0.2.5:
    resolution: {integrity: sha512-c8eMDb1WwZcE02WVjHoOmUVk7fnKU/RmUcosHACglrWAuPQsEJv+E8430sXj6jNc1jHw0zrS16aCjQh4BcEb4A==}
    dev: false

  /mustache/4.2.0:
    resolution: {integrity: sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==}
    hasBin: true
    dev: false

  /mz/2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0
    dev: true

  /nanoid/3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  /nanoid/3.3.6:
    resolution: {integrity: sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: false

  /nanoid/5.1.5:
    resolution: {integrity: sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==}
    engines: {node: ^18 || >=20}
    hasBin: true
    dev: false

  /napi-postinstall/0.3.0:
    resolution: {integrity: sha512-M7NqKyhODKV1gRLdkwE7pDsZP2/SC2a2vHkOYh9MCpKMbWVfyVfUw5MaH83Fv6XMjxr5jryUp3IDDL9rlxsTeA==}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}
    hasBin: true
    dev: true

  /natural-compare/1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    dev: true

  /negotiator/0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}
    dev: false

  /neo-async/2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}
    dev: true

  /next-themes/0.3.0_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-/QHIrsYpd6Kfk7xakK4svpDI5mmXP0gfvCoJdGpZQ2TOrQZmsW0QxjaiLn8wbIKjtm4BTSqLoix4lxYYOnLJ/w==}
    peerDependencies:
      react: ^16.8 || ^17 || ^18
      react-dom: ^16.8 || ^17 || ^18
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /next/14.2.23_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-mjN3fE6u/tynneLiEg56XnthzuYw+kD7mCujgVqioxyPqbmiotUCGJpIZGS/VaPg3ZDT1tvWxiVyRzeqJFm/kw==}
    engines: {node: '>=18.17.0'}
    hasBin: true
    peerDependencies:
      '@opentelemetry/api': ^1.1.0
      '@playwright/test': ^1.41.2
      react: ^18.2.0
      react-dom: ^18.2.0
      sass: ^1.3.0
    peerDependenciesMeta:
      '@opentelemetry/api':
        optional: true
      '@playwright/test':
        optional: true
      sass:
        optional: true
    dependencies:
      '@next/env': 14.2.23
      '@swc/helpers': 0.5.5
      busboy: 1.6.0
      caniuse-lite: 1.0.30001726
      graceful-fs: 4.2.11
      postcss: 8.4.31
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      styled-jsx: 5.1.1_react@18.2.0
    optionalDependencies:
      '@next/swc-darwin-arm64': 14.2.23
      '@next/swc-darwin-x64': 14.2.23
      '@next/swc-linux-arm64-gnu': 14.2.23
      '@next/swc-linux-arm64-musl': 14.2.23
      '@next/swc-linux-x64-gnu': 14.2.23
      '@next/swc-linux-x64-musl': 14.2.23
      '@next/swc-win32-arm64-msvc': 14.2.23
      '@next/swc-win32-ia32-msvc': 14.2.23
      '@next/swc-win32-x64-msvc': 14.2.23
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-macros
    dev: false

  /node-domexception/1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}
    deprecated: Use your platform's native DOMException instead
    dev: false

  /node-fetch/2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: 5.0.0
    dev: false

  /node-gyp-build-optional-packages/5.2.2:
    resolution: {integrity: sha512-s+w+rBWnpTMwSFbaE0UXsRlg7hU4FjekKU4eyAih5T8nJuNZT1nNsskXpxmeqSK9UzkBl6UgRlnKc8hz8IEqOw==}
    hasBin: true
    dependencies:
      detect-libc: 2.0.4
    dev: false
    optional: true

  /node-releases/2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}
    dev: true

  /normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /nth-check/2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}
    dependencies:
      boolbase: 1.0.0
    dev: false

  /object-assign/4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  /object-hash/3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}
    dev: true

  /object-inspect/1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  /object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}
    dev: true

  /object.assign/4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1
    dev: true

  /object.entries/1.1.9:
    resolution: {integrity: sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /object.fromentries/2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
    dev: true

  /object.groupby/1.0.3:
    resolution: {integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
    dev: true

  /object.values/1.2.1:
    resolution: {integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /on-finished/2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: 1.1.1
    dev: false

  /once/1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2
    dev: true

  /openai/4.104.0_zod@3.25.70:
    resolution: {integrity: sha512-p99EFNsA/yX6UhVO93f5kJsDRLAg+CTA2RBqdHK4RtK8u5IJw32Hyb2dTGKbnnFmnuoBv5r7Z2CURI9sGZpSuA==}
    hasBin: true
    peerDependencies:
      ws: ^8.18.0
      zod: ^3.23.8
    peerDependenciesMeta:
      ws:
        optional: true
      zod:
        optional: true
    dependencies:
      '@types/node': 18.19.115
      '@types/node-fetch': 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
      zod: 3.25.70
    transitivePeerDependencies:
      - encoding
    dev: false

  /openapi-types/12.1.3:
    resolution: {integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==}
    dev: false

  /optics-ts/2.4.1:
    resolution: {integrity: sha512-HaYzMHvC80r7U/LqAd4hQyopDezC60PO2qF5GuIwALut2cl5rK1VWHsqTp0oqoJJWjiv6uXKqsO+Q2OO0C3MmQ==}
    dev: false

  /optionator/0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5
    dev: true

  /orderedmap/2.1.1:
    resolution: {integrity: sha512-TvAWxi0nDe1j/rtMcWcIj94+Ffe6n7zhow33h40SKxmsmozs6dz/e+EajymfoFcHd7sxNn8yHM8839uixMOV6g==}
    dev: false

  /own-keys/1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0
    dev: true

  /p-finally/1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==}
    engines: {node: '>=4'}
    dev: false

  /p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate/5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-queue/6.6.2:
    resolution: {integrity: sha512-RwFpb72c/BhQLEXIZ5K2e+AhgNVmIejGlTgiB9MzZ0e93GRvqZ7uSi0dvRF7/XIXDeNkra2fNHBxTyPDGySpjQ==}
    engines: {node: '>=8'}
    dependencies:
      eventemitter3: 4.0.7
      p-timeout: 3.2.0
    dev: false

  /p-retry/4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1
    dev: false

  /p-timeout/3.2.0:
    resolution: {integrity: sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==}
    engines: {node: '>=8'}
    dependencies:
      p-finally: 1.0.0
    dev: false

  /package-json-from-dist/1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}
    dev: true

  /papaparse/5.5.3:
    resolution: {integrity: sha512-5QvjGxYVjxO59MGU2lHVYpRWBBtKHnlIAcSe1uNFCkkptUh63NFRj0FJQm7nR67puEruUci/ZkjmEFrjCAyP4A==}
    dev: false

  /parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse5-htmlparser2-tree-adapter/6.0.1:
    resolution: {integrity: sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA==}
    dependencies:
      parse5: 6.0.1
    dev: false

  /parse5/6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}
    dev: false

  /parseurl/1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute/1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: true

  /path-scurry/1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2
    dev: true

  /path-to-regexp/0.1.12:
    resolution: {integrity: sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==}
    dev: false

  /path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: true

  /performance-now/2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}
    dev: false

  /picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  /picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}
    dev: true

  /picomatch/4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==}
    engines: {node: '>=12'}
    dev: true

  /pify/2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}
    dev: true

  /pirates/4.0.7:
    resolution: {integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==}
    engines: {node: '>= 6'}
    dev: true

  /possible-typed-array-names/1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}
    dev: true

  /postcss-import/15.1.0_postcss@8.5.6:
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10
    dev: true

  /postcss-js/4.0.1_postcss@8.5.6:
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.6
    dev: true

  /postcss-load-config/4.0.2_postcss@8.5.6+ts-node@10.9.2:
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true
    dependencies:
      lilconfig: 3.1.3
      postcss: 8.5.6
      ts-node: 10.9.2_41655fe179e000f935c12012a5853c75
      yaml: 2.8.0
    dev: true

  /postcss-nested/6.2.0_postcss@8.5.6:
    resolution: {integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-selector-parser/6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-selector-parser/6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: true

  /postcss/8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: false

  /postcss/8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: true

  /prelude-ls/1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prettier-plugin-tailwindcss/0.5.14_prettier@3.6.2:
    resolution: {integrity: sha512-Puaz+wPUAhFp8Lo9HuciYKM2Y2XExESjeT+9NQoVFXZsPPnc9VYss2SpxdQ6vbatmt8/4+SN0oe0I1cPDABg9Q==}
    engines: {node: '>=14.21.3'}
    peerDependencies:
      '@ianvs/prettier-plugin-sort-imports': '*'
      '@prettier/plugin-pug': '*'
      '@shopify/prettier-plugin-liquid': '*'
      '@trivago/prettier-plugin-sort-imports': '*'
      '@zackad/prettier-plugin-twig-melody': '*'
      prettier: ^3.0
      prettier-plugin-astro: '*'
      prettier-plugin-css-order: '*'
      prettier-plugin-import-sort: '*'
      prettier-plugin-jsdoc: '*'
      prettier-plugin-marko: '*'
      prettier-plugin-organize-attributes: '*'
      prettier-plugin-organize-imports: '*'
      prettier-plugin-sort-imports: '*'
      prettier-plugin-style-order: '*'
      prettier-plugin-svelte: '*'
    peerDependenciesMeta:
      '@ianvs/prettier-plugin-sort-imports':
        optional: true
      '@prettier/plugin-pug':
        optional: true
      '@shopify/prettier-plugin-liquid':
        optional: true
      '@trivago/prettier-plugin-sort-imports':
        optional: true
      '@zackad/prettier-plugin-twig-melody':
        optional: true
      prettier-plugin-astro:
        optional: true
      prettier-plugin-css-order:
        optional: true
      prettier-plugin-import-sort:
        optional: true
      prettier-plugin-jsdoc:
        optional: true
      prettier-plugin-marko:
        optional: true
      prettier-plugin-organize-attributes:
        optional: true
      prettier-plugin-organize-imports:
        optional: true
      prettier-plugin-sort-imports:
        optional: true
      prettier-plugin-style-order:
        optional: true
      prettier-plugin-svelte:
        optional: true
    dependencies:
      prettier: 3.6.2
    dev: true

  /prettier/3.6.2:
    resolution: {integrity: sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /prisma/5.22.0:
    resolution: {integrity: sha512-vtpjW3XuYCSnMsNVBjLMNkTj6OZbudcPPTPYHqX0CJfpcdWciI1dM8uHETwmDxxiqEwCIE6WvXucWUetJgfu/A==}
    engines: {node: '>=16.13'}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@prisma/engines': 5.22.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: false

  /prismjs/1.30.0:
    resolution: {integrity: sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==}
    engines: {node: '>=6'}
    dev: false

  /prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  /prosemirror-commands/1.7.1:
    resolution: {integrity: sha512-rT7qZnQtx5c0/y/KlYaGvtG411S97UaL6gdp6RIZ23DLHanMYLyfGBV5DtSnZdthQql7W+lEVbpSfwtO8T+L2w==}
    dependencies:
      prosemirror-model: 1.25.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.4
    dev: false

  /prosemirror-history/1.4.1:
    resolution: {integrity: sha512-2JZD8z2JviJrboD9cPuX/Sv/1ChFng+xh2tChQ2X4bB2HeK+rra/bmJ3xGntCcjhOqIzSDG6Id7e8RJ9QPXLEQ==}
    dependencies:
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.4
      prosemirror-view: 1.40.0
      rope-sequence: 1.3.4
    dev: false

  /prosemirror-keymap/1.2.3:
    resolution: {integrity: sha512-4HucRlpiLd1IPQQXNqeo81BGtkY8Ai5smHhKW9jjPKRc2wQIxksg7Hl1tTI2IfT2B/LgX6bfYvXxEpJl7aKYKw==}
    dependencies:
      prosemirror-state: 1.4.3
      w3c-keyname: 2.2.8
    dev: false

  /prosemirror-markdown/1.13.2:
    resolution: {integrity: sha512-FPD9rHPdA9fqzNmIIDhhnYQ6WgNoSWX9StUZ8LEKapaXU9i6XgykaHKhp6XMyXlOWetmaFgGDS/nu/w9/vUc5g==}
    dependencies:
      '@types/markdown-it': 14.1.2
      markdown-it: 14.1.0
      prosemirror-model: 1.25.1
    dev: false

  /prosemirror-model/1.25.1:
    resolution: {integrity: sha512-AUvbm7qqmpZa5d9fPKMvH1Q5bqYQvAZWOGRvxsB6iFLyycvC9MwNemNVjHVrWgjaoxAfY8XVg7DbvQ/qxvI9Eg==}
    dependencies:
      orderedmap: 2.1.1
    dev: false

  /prosemirror-schema-basic/1.2.4:
    resolution: {integrity: sha512-ELxP4TlX3yr2v5rM7Sb70SqStq5NvI15c0j9j/gjsrO5vaw+fnnpovCLEGIcpeGfifkuqJwl4fon6b+KdrODYQ==}
    dependencies:
      prosemirror-model: 1.25.1
    dev: false

  /prosemirror-schema-list/1.5.1:
    resolution: {integrity: sha512-927lFx/uwyQaGwJxLWCZRkjXG0p48KpMj6ueoYiu4JX05GGuGcgzAy62dfiV8eFZftgyBUvLx76RsMe20fJl+Q==}
    dependencies:
      prosemirror-model: 1.25.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.4
    dev: false

  /prosemirror-state/1.4.3:
    resolution: {integrity: sha512-goFKORVbvPuAQaXhpbemJFRKJ2aixr+AZMGiquiqKxaucC6hlpHNZHWgz5R7dS4roHiwq9vDctE//CZ++o0W1Q==}
    dependencies:
      prosemirror-model: 1.25.1
      prosemirror-transform: 1.10.4
      prosemirror-view: 1.40.0
    dev: false

  /prosemirror-transform/1.10.4:
    resolution: {integrity: sha512-pwDy22nAnGqNR1feOQKHxoFkkUtepoFAd3r2hbEDsnf4wp57kKA36hXsB3njA9FtONBEwSDnDeCiJe+ItD+ykw==}
    dependencies:
      prosemirror-model: 1.25.1
    dev: false

  /prosemirror-view/1.40.0:
    resolution: {integrity: sha512-2G3svX0Cr1sJjkD/DYWSe3cfV5VPVTBOxI9XQEGWJDFEpsZb/gh4MV29ctv+OJx2RFX4BLt09i+6zaGM/ldkCw==}
    dependencies:
      prosemirror-model: 1.25.1
      prosemirror-state: 1.4.3
      prosemirror-transform: 1.10.4
    dev: false

  /proxy-addr/2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1
    dev: false

  /proxy-compare/2.6.0:
    resolution: {integrity: sha512-8xuCeM3l8yqdmbPoYeLbrAXCBWu19XEYc5/F28f5qOaoAIMyfmBUkl5axiK+x9olUvRlcekvnm98AP9RDngOIw==}
    dev: false

  /punycode.js/2.3.1:
    resolution: {integrity: sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==}
    engines: {node: '>=6'}
    dev: false

  /punycode/2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}
    dev: true

  /pure-rand/6.1.0:
    resolution: {integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==}
    dev: false

  /qs/6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0
    dev: false

  /qs/6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0
    dev: false

  /queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: true

  /raf/3.4.1:
    resolution: {integrity: sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==}
    dependencies:
      performance-now: 2.1.0
    dev: false

  /randombytes/2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /range-parser/1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}
    dev: false

  /raw-body/2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0
    dev: false

  /raw-body/3.0.0:
    resolution: {integrity: sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==}
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.6.3
      unpipe: 1.0.0
    dev: false

  /re-resizable/6.11.2_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-2xI2P3OHs5qw7K0Ud1aLILK6MQxW50TcO+DetD9eIV58j84TqYeHoZcL9H4GXFXXIh7afhH8mv5iUCXII7OW7A==}
    peerDependencies:
      react: ^16.13.1 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.13.1 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /react-colorful/5.6.1_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-1exovf0uGTGyq5mXQT0zgQ80uvj2PCwvF8zY1RN9/vbJVSjSo3fsB/4L3ObbF7u70NduSiK4xu4Y6q1MHoUGEw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /react-day-picker/8.10.1_date-fns@3.6.0+react@18.2.0:
    resolution: {integrity: sha512-TMx7fNbhLk15eqcMt+7Z7S2KF7mfTId/XJDjKE8f+IUcFn0l08/kI4FiYTL/0yuOLmEcbR4Fwe3GJf/NiiMnPA==}
    peerDependencies:
      date-fns: ^2.28.0 || ^3.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      date-fns: 3.6.0
      react: 18.2.0
    dev: false

  /react-dnd-html5-backend/16.0.1:
    resolution: {integrity: sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw==}
    dependencies:
      dnd-core: 16.0.1
    dev: false

  /react-dnd/16.0.1_fef9396d7cc8c8d91fc915a58da7a30e:
    resolution: {integrity: sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==}
    peerDependencies:
      '@types/hoist-non-react-statics': '>= 3.3.1'
      '@types/node': '>= 12'
      '@types/react': '>= 16'
      react: '>= 16.14'
    peerDependenciesMeta:
      '@types/hoist-non-react-statics':
        optional: true
      '@types/node':
        optional: true
      '@types/react':
        optional: true
    dependencies:
      '@react-dnd/invariant': 4.0.2
      '@react-dnd/shallowequal': 4.0.2
      '@types/node': 20.19.4
      '@types/react': 18.3.23
      dnd-core: 16.0.1
      fast-deep-equal: 3.1.3
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
    dev: false

  /react-dom/18.2.0_react@18.2.0:
    resolution: {integrity: sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==}
    peerDependencies:
      react: ^18.2.0
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.2
    dev: false

  /react-dropzone/14.3.8_react@18.2.0:
    resolution: {integrity: sha512-sBgODnq+lcA4P296DY4wacOZz3JFpD99fp+hb//iBO2HHnyeZU3FwWyXJ6salNpqQdsZrgMrotuko/BdJMV8Ug==}
    engines: {node: '>= 10.13'}
    peerDependencies:
      react: '>= 16.8 || 18.0.0'
    dependencies:
      attr-accept: 2.2.5
      file-selector: 2.1.2
      prop-types: 15.8.1
      react: 18.2.0
    dev: false

  /react-file-picker/0.0.6:
    resolution: {integrity: sha512-EnRQCEeC5YpSaCm/pbUi7zroP6lMgT7SqQTFS0ZNJXlcA8Cw2zDvEIoP/SkQ8u5Lbp+L2cyoxiy7ZBVtXk5hGw==}
    dev: false

  /react-fontpicker-ts/1.2.0_react@18.2.0:
    resolution: {integrity: sha512-IRpNyTm3WRqR1SBiFCAEvAD52b/Htgo8gI5vNtSVHYRPMbPt1SDPkNMSCkujHMLmP/hCjD/6ZzfFcpdYF1gRqQ==}
    peerDependencies:
      react: '>=16.8'
    dependencies:
      react: 18.2.0
    dev: false

  /react-hook-form/7.59.0_react@18.2.0:
    resolution: {integrity: sha512-kmkek2/8grqarTJExFNjy+RXDIP8yM+QTl3QL6m6Q8b2bih4ltmiXxH7T9n+yXNK477xPh5yZT/6vD8sYGzJTA==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18 || ^19
    dependencies:
      react: 18.2.0
    dev: false

  /react-icons-picker/1.0.9_dd472908a3e86255eec0e40e6bef4372:
    resolution: {integrity: sha512-EkBJ5UcHjMQQSGqNkNupZ0vfmskAUvg26bsTKXt/I8ddqybIGHJBHs4qOMy9b2elOoWQk3ZRBviXQGe1x+Z+fQ==}
    peerDependencies:
      react: ^18.2.0
      react-dom: ^18.2.0
      react-icons: ^4.4.0
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-icons: 5.5.0_react@18.2.0
    dev: false

  /react-icons/5.5.0_react@18.2.0:
    resolution: {integrity: sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==}
    peerDependencies:
      react: '*'
    dependencies:
      react: 18.2.0
    dev: false

  /react-intersection-observer/9.16.0_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-w9nJSEp+DrW9KmQmeWHQyfaP6b03v+TdXynaoA964Wxt7mdR3An11z4NNCQgL4gKSK7y1ver2Fq+JKH6CWEzUA==}
    peerDependencies:
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      react-dom:
        optional: true
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  /react-is/18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}
    dev: false

  /react-remove-scroll-bar/2.3.8_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
      react-style-singleton: 2.2.3_888dbdc24f3e5b913723df3d4309886e
      tslib: 2.8.1
    dev: false

  /react-remove-scroll/2.7.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
      react-remove-scroll-bar: 2.3.8_888dbdc24f3e5b913723df3d4309886e
      react-style-singleton: 2.2.3_888dbdc24f3e5b913723df3d4309886e
      tslib: 2.8.1
      use-callback-ref: 1.3.3_888dbdc24f3e5b913723df3d4309886e
      use-sidecar: 1.1.3_888dbdc24f3e5b913723df3d4309886e
    dev: false

  /react-resizable-panels/2.1.9_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-z77+X08YDIrgAes4jl8xhnUu1LNIRp4+E7cv4xHmLOxxUPO/ML7PSrE813b90vj7xvQ1lcf7g2uA9GeMZonjhQ==}
    peerDependencies:
      react: ^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /react-smooth/4.0.4_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-gnGKTpYwqL0Iii09gHobNolvX4Kiq4PKx6eWBCYYix+8cdw+cGo3do906l1NBPKkSWx1DghC1dlWG9L2uGd61Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      fast-equals: 5.2.2
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-transition-group: 4.4.5_react-dom@18.2.0+react@18.2.0
    dev: false

  /react-style-singleton/2.2.3_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      get-nonce: 1.0.1
      react: 18.2.0
      tslib: 2.8.1
    dev: false

  /react-textarea-autosize/8.5.9_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-U1DGlIQN5AwgjTyOEnI1oCcMuEr1pv1qOtklB2l4nyMGbHzWrI0eFsYK0zos2YWqAolJyG0IWJaqWmWj5ETh0A==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      '@babel/runtime': 7.27.6
      react: 18.2.0
      use-composed-ref: 1.4.0_888dbdc24f3e5b913723df3d4309886e
      use-latest: 1.3.0_888dbdc24f3e5b913723df3d4309886e
    transitivePeerDependencies:
      - '@types/react'
    dev: false

  /react-tracked/1.7.14_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-6UMlgQeRAGA+uyYzuQGm7kZB6ZQYFhc7sntgP7Oxwwd6M0Ud/POyb4K3QWT1eXvoifSa80nrAWnXWFGpOvbwkw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '*'
      react-native: '*'
      scheduler: '>=0.19.0'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      proxy-compare: 2.6.0
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      use-context-selector: 1.4.4_react-dom@18.2.0+react@18.2.0
    dev: false

  /react-transition-group/4.4.5_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'
    dependencies:
      '@babel/runtime': 7.27.6
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /react/18.2.0:
    resolution: {integrity: sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /read-cache/1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0
    dev: true

  /readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: true

  /recharts-scale/0.4.5:
    resolution: {integrity: sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==}
    dependencies:
      decimal.js-light: 2.5.1
    dev: false

  /recharts/2.15.4_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-UT/q6fwS3c1dHbXv2uFgYJ9BMFHu3fwnd7AYZaEQhXuYQ4hgsxLvsUXzGdKeZrW5xopzDCvuA2N41WJ88I7zIw==}
    engines: {node: '>=14'}
    peerDependencies:
      react: ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      clsx: 2.1.1
      eventemitter3: 4.0.7
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      react-is: 18.3.1
      react-smooth: 4.0.4_react-dom@18.2.0+react@18.2.0
      recharts-scale: 0.4.5
      tiny-invariant: 1.3.3
      victory-vendor: 36.9.2
    dev: false

  /redux/4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}
    dependencies:
      '@babel/runtime': 7.27.6
    dev: false

  /reflect.getprototypeof/1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1
    dev: true

  /regexp.prototype.flags/1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2
    dev: true

  /remark-gfm/4.0.0:
    resolution: {integrity: sha512-U92vJgBPkbw4Zfu/IiW2oTZLSL3Zpv+uI7My2eq8JxKgqraFdU8YUGicEJCEgSbeaG+QDFqIcwwfMTOEelPxuA==}
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-gfm: 3.1.0
      micromark-extension-gfm: 3.0.0
      remark-parse: 11.0.0
      remark-stringify: 11.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-parse/11.0.0:
    resolution: {integrity: sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==}
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-from-markdown: 2.0.2
      micromark-util-types: 2.0.2
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color
    dev: false

  /remark-stringify/11.0.0:
    resolution: {integrity: sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==}
    dependencies:
      '@types/mdast': 4.0.4
      mdast-util-to-markdown: 2.1.2
      unified: 11.0.5
    dev: false

  /require-from-string/2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}
    dev: true

  /resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-pkg-maps/1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}
    dev: true

  /resolve/1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /resolve/2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /retry/0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}
    dev: false

  /reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /rope-sequence/1.3.4:
    resolution: {integrity: sha512-UT5EDe2cu2E/6O4igUr5PSFs23nvvukicWHx6GnOPlHAiiYbzNuCRQCuiUdHJQcqKalLKlrYJnjY0ySGsXNQXQ==}
    dev: false

  /run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /safe-array-concat/1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5
    dev: true

  /safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  /safe-push-apply/1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5
    dev: true

  /safe-regex-test/1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1
    dev: true

  /safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    dev: false

  /scheduler/0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /schema-utils/4.3.2:
    resolution: {integrity: sha512-Gn/JaSk/Mt9gYubxTtSn/QCV4em9mpAPiR1rqy/Ocu19u/G9J5WWdNoUT4SiV6mFC3y6cxyFcFwdzPM3FgxGAQ==}
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1
      ajv-keywords: 5.1.0_ajv@8.17.1
    dev: true

  /scroll-into-view-if-needed/3.1.0:
    resolution: {integrity: sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ==}
    dependencies:
      compute-scroll-into-view: 3.1.1
    dev: false

  /secure-json-parse/2.7.0:
    resolution: {integrity: sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==}
    dev: false

  /semver/6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true
    dev: true

  /semver/7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  /send/0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    dev: false

  /serialize-javascript/6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}
    dependencies:
      randombytes: 2.1.0
    dev: true

  /serve-static/1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    dev: false

  /set-function-length/1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
    dev: true

  /set-function-name/2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2
    dev: true

  /set-proto/1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
    dev: true

  /setprototypeof/1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}
    dev: false

  /shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /side-channel-list/1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  /side-channel-map/1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  /side-channel-weakmap/1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  /side-channel/1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  /signal-exit/4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}
    dev: true

  /simple-wcswidth/1.1.2:
    resolution: {integrity: sha512-j7piyCjAeTDSjzTSQ7DokZtMNwNlEAyxqSZeCS+CXH7fJ4jx3FuJ/mTW3mE+6JLs4VJBbcll0Kjn+KXI5t21Iw==}
    dev: false

  /slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /slate-history/0.113.1_slate@0.103.0:
    resolution: {integrity: sha512-J9NSJ+UG2GxoW0lw5mloaKcN0JI0x2IA5M5FxyGiInpn+QEutxT1WK7S/JneZCMFJBoHs1uu7S7e6pxQjubHmQ==}
    peerDependencies:
      slate: '>=0.65.3'
    dependencies:
      is-plain-object: 5.0.0
      slate: 0.103.0
    dev: false

  /slate-hyperscript/0.100.0_slate@0.103.0:
    resolution: {integrity: sha512-fb2KdAYg6RkrQGlqaIi4wdqz3oa0S4zKNBJlbnJbNOwa23+9FLD6oPVx9zUGqCSIpy+HIpOeqXrg0Kzwh/Ii4A==}
    peerDependencies:
      slate: '>=0.65.3'
    dependencies:
      is-plain-object: 5.0.0
      slate: 0.103.0
    dev: false

  /slate-react/0.110.3_23b6d8887d45f82da5f0400dcb42c149:
    resolution: {integrity: sha512-AS8PPjwmsFS3Lq0MOEegLVlFoxhyos68G6zz2nW4sh3WeTXV7pX0exnwtY1a/docn+J3LGQO11aZXTenPXA/kg==}
    peerDependencies:
      react: '>=18.2.0'
      react-dom: '>=18.2.0'
      slate: '>=0.99.0'
    dependencies:
      '@juggle/resize-observer': 3.4.0
      direction: 1.0.4
      is-hotkey: 0.2.0
      is-plain-object: 5.0.0
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
      scroll-into-view-if-needed: 3.1.0
      slate: 0.103.0
      tiny-invariant: 1.3.1
    dev: false

  /slate/0.103.0:
    resolution: {integrity: sha512-eCUOVqUpADYMZ59O37QQvUdnFG+8rin0OGQAXNHvHbQeVJ67Bu0spQbcy621vtf8GQUXTEQBlk6OP9atwwob4w==}
    dependencies:
      immer: 10.1.1
      is-plain-object: 5.0.0
      tiny-warning: 1.0.3
    dev: false

  /slick/1.12.2:
    resolution: {integrity: sha512-4qdtOGcBjral6YIBCWJ0ljFSKNLz9KkhbWtuGvUyRowl1kxfuE1x/Z/aJcaiilpb3do9bl5K7/1h9XC5wWpY/A==}
    dev: false

  /sonner/1.7.4_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-DIS8z4PfJRbIyfVFDVnK9rO3eYDtse4Omcm6bt0oEr5/jtLgysmjuBl1frJ9E/EQZrFmKx2A8m/s5s9CRXIzhw==}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /source-map-js/1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  /source-map-support/0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1
    dev: true

  /source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /sqids/0.3.0:
    resolution: {integrity: sha512-lOQK1ucVg+W6n3FhRwwSeUijxe93b51Bfz5PMRMihVf1iVkl82ePQG7V5vwrhzB11v0NtsR25PSZRGiSomJaJw==}
    dev: false

  /stable-hash/0.0.5:
    resolution: {integrity: sha512-+L3ccpzibovGXFK+Ap/f8LOS0ahMrHTf3xu7mMLSpEGU0EO9ucaysSylKo9eRDFNhWve/y275iPmIZ4z39a9iA==}
    dev: true

  /statuses/2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /stop-iteration-iterator/1.1.0:
    resolution: {integrity: sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      internal-slot: 1.1.0
    dev: true

  /streamsearch/1.1.0:
    resolution: {integrity: sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==}
    engines: {node: '>=10.0.0'}
    dev: false

  /string-width/4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string-width/5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: true

  /string.prototype.includes/2.0.1:
    resolution: {integrity: sha512-o7+c9bW6zpAdJHTtujeePODAhkuicdAryFsfVKwA+wGw89wJ4GTY484WTucM9hLtDEOpOvI+aHnzqnC5lHp4Rg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.24.0
    dev: true

  /string.prototype.matchall/4.0.12:
    resolution: {integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0
    dev: true

  /string.prototype.repeat/1.0.0:
    resolution: {integrity: sha512-0u/TldDbKD8bFCQ/4f5+mNRrXwZ8hg2w7ZR8wa16e8z9XpePWl3eGEcUD0OXpEH/VJH/2G3gjUtR3ZOiBe2S/w==}
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.24.0
    dev: true

  /string.prototype.trim/1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.24.0
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2
    dev: true

  /string.prototype.trimend/1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /string.prototype.trimstart/1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /strip-ansi/6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-ansi/7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.1.0
    dev: true

  /strip-bom/3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}
    dev: true

  /strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: true

  /styled-jsx/5.1.1_react@18.2.0:
    resolution: {integrity: sha512-pW7uC1l4mBZ8ugbiZrcIsiIvVx1UmTfw7UkC3Um2tmfUq9Bhk8IiyEIPl6F8agHgjzku6j0xQEZbfA5uSgSaCw==}
    engines: {node: '>= 12.0.0'}
    peerDependencies:
      '@babel/core': '*'
      babel-plugin-macros: '*'
      react: '>= 16.8.0 || 17.x.x || ^18.0.0-0'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      babel-plugin-macros:
        optional: true
    dependencies:
      client-only: 0.0.1
      react: 18.2.0
    dev: false

  /sucrase/3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.7
      ts-interface-checker: 0.1.13
    dev: true

  /supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0

  /supports-color/8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /swr/2.2.0_react@18.2.0:
    resolution: {integrity: sha512-AjqHOv2lAhkuUdIiBu9xbuettzAzWXmCEcLONNKJRba87WAefz8Ca9d6ds/SzrPc235n1IxWYdhJ2zF3MNUaoQ==}
    peerDependencies:
      react: ^16.11.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
      use-sync-external-store: 1.5.0_react@18.2.0
    dev: false

  /swr/2.3.4_react@18.2.0:
    resolution: {integrity: sha512-bYd2lrhc+VarcpkgWclcUi92wYCpOgMws9Sd1hG1ntAu0NEy+14CbotuFjshBU2kt9rYj9TSmDcybpxpeTU1fg==}
    peerDependencies:
      react: ^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      dequal: 2.0.3
      react: 18.2.0
      use-sync-external-store: 1.5.0_react@18.2.0
    dev: false

  /tabbable/6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}
    dev: false

  /tailwind-merge/2.6.0:
    resolution: {integrity: sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA==}
    dev: false

  /tailwind-scrollbar-hide/2.0.0_tailwindcss@3.4.17:
    resolution: {integrity: sha512-lqiIutHliEiODwBRHy4G2+Tcayo2U7+3+4frBmoMETD72qtah+XhOk5XcPzC1nJvXhXUdfl2ajlMhUc2qC6CIg==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || >= 4.0.0 || >= 4.0.0-beta.8 || >= 4.0.0-alpha.20'
    dependencies:
      tailwindcss: 3.4.17_ts-node@10.9.2
    dev: true

  /tailwindcss-animate/1.0.7_tailwindcss@3.4.17:
    resolution: {integrity: sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA==}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'
    dependencies:
      tailwindcss: 3.4.17_ts-node@10.9.2
    dev: true

  /tailwindcss-scrollbar/0.1.0_tailwindcss@3.4.17:
    resolution: {integrity: sha512-egipxw4ooQDh94x02XQpPck0P0sfwazwoUGfA9SedPATIuYDR+6qe8d31Gl7YsSMRiOKDkkqfI0kBvEw9lT/Hg==}
    peerDependencies:
      tailwindcss: '>= 2.x.x'
    dependencies:
      tailwindcss: 3.4.17_ts-node@10.9.2
    dev: true

  /tailwindcss/3.4.17_ts-node@10.9.2:
    resolution: {integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.6
      postcss-import: 15.1.0_postcss@8.5.6
      postcss-js: 4.0.1_postcss@8.5.6
      postcss-load-config: 4.0.2_postcss@8.5.6+ts-node@10.9.2
      postcss-nested: 6.2.0_postcss@8.5.6
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node
    dev: true

  /tapable/2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}
    dev: true

  /terser-webpack-plugin/5.3.14_webpack@5.99.9:
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': 0.3.29
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.43.1
      webpack: 5.99.9
    dev: true

  /terser/5.43.1:
    resolution: {integrity: sha512-+6erLbBm0+LROX2sPXlUYx/ux5PyE9K/a92Wrt6oA+WDAoFTdpHE5tCYCI5PNzq2y8df4rA+QgHLJuR4jNymsg==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': 0.3.10
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21
    dev: true

  /text-table/0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}
    dev: true

  /thenify-all/1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1
    dev: true

  /thenify/3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0
    dev: true

  /throttleit/2.1.0:
    resolution: {integrity: sha512-nt6AMGKW1p/70DF/hGBdJB57B8Tspmbp5gfJ8ilhLnt7kkr2ye7hzD6NVG8GGErk2HWF34igrL2CXmNIkzKqKw==}
    engines: {node: '>=18'}
    dev: false

  /tiny-invariant/1.3.1:
    resolution: {integrity: sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw==}
    dev: false

  /tiny-invariant/1.3.3:
    resolution: {integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==}
    dev: false

  /tiny-warning/1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}
    dev: false

  /tinyglobby/0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}
    dependencies:
      fdir: 6.4.6_picomatch@4.0.2
      picomatch: 4.0.2
    dev: true

  /to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0
    dev: true

  /together-ai/0.7.0:
    resolution: {integrity: sha512-/be/HOecBSwRTDHB14vCvHbp1WiNsFxyS4pJlyBoMup1X3n7xD1b/Gm5Z5amlKzD2zll9Y5wscDk7Ut5OsT1nA==}
    dependencies:
      '@types/node': 18.19.115
      '@types/node-fetch': 2.6.12
      abort-controller: 3.0.0
      agentkeepalive: 4.6.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding
    dev: false

  /toggle-selection/1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}
    dev: false

  /toidentifier/1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}
    dev: false

  /tr46/0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}
    dev: false

  /trough/2.2.0:
    resolution: {integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==}
    dev: false

  /ts-api-utils/1.4.3_typescript@5.8.3:
    resolution: {integrity: sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==}
    engines: {node: '>=16'}
    peerDependencies:
      typescript: '>=4.2.0'
    dependencies:
      typescript: 5.8.3
    dev: true

  /ts-interface-checker/0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}
    dev: true

  /ts-node/10.9.2_41655fe179e000f935c12012a5853c75:
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.11
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 20.19.4
      acorn: 8.15.0
      acorn-walk: 8.3.4
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.8.3
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1
    dev: true

  /tsconfig-paths/3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0
    dev: true

  /tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  /type-check/0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.2.1
    dev: true

  /type-fest/0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}
    dev: true

  /type-is/1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35
    dev: false

  /type-is/2.0.1:
    resolution: {integrity: sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==}
    engines: {node: '>= 0.6'}
    dependencies:
      content-type: 1.0.5
      media-typer: 1.1.0
      mime-types: 3.0.1
    dev: false

  /typed-array-buffer/1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15
    dev: true

  /typed-array-byte-length/1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
    dev: true

  /typed-array-byte-offset/1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10
    dev: true

  /typed-array-length/1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10
    dev: true

  /typescript/5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  /uc.micro/2.1.0:
    resolution: {integrity: sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==}
    dev: false

  /unbox-primitive/1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1
    dev: true

  /undici-types/5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}
    dev: false

  /undici-types/6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  /unified/11.0.5:
    resolution: {integrity: sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==}
    dependencies:
      '@types/unist': 3.0.3
      bail: 2.0.2
      devlop: 1.1.0
      extend: 3.0.2
      is-plain-obj: 4.1.0
      trough: 2.2.0
      vfile: 6.0.3
    dev: false

  /unist-util-is/6.0.0:
    resolution: {integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==}
    dependencies:
      '@types/unist': 3.0.3
    dev: false

  /unist-util-stringify-position/4.0.0:
    resolution: {integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==}
    dependencies:
      '@types/unist': 3.0.3
    dev: false

  /unist-util-visit-parents/6.0.1:
    resolution: {integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==}
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
    dev: false

  /unist-util-visit/5.0.0:
    resolution: {integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==}
    dependencies:
      '@types/unist': 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1
    dev: false

  /unpipe/1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}
    dev: false

  /unrs-resolver/1.10.1:
    resolution: {integrity: sha512-EFrL7Hw4kmhZdwWO3dwwFJo6hO3FXuQ6Bg8BK/faHZ9m1YxqBS31BNSTxklIQkxK/4LlV8zTYnPsIRLBzTzjCA==}
    requiresBuild: true
    dependencies:
      napi-postinstall: 0.3.0
    optionalDependencies:
      '@unrs/resolver-binding-android-arm-eabi': 1.10.1
      '@unrs/resolver-binding-android-arm64': 1.10.1
      '@unrs/resolver-binding-darwin-arm64': 1.10.1
      '@unrs/resolver-binding-darwin-x64': 1.10.1
      '@unrs/resolver-binding-freebsd-x64': 1.10.1
      '@unrs/resolver-binding-linux-arm-gnueabihf': 1.10.1
      '@unrs/resolver-binding-linux-arm-musleabihf': 1.10.1
      '@unrs/resolver-binding-linux-arm64-gnu': 1.10.1
      '@unrs/resolver-binding-linux-arm64-musl': 1.10.1
      '@unrs/resolver-binding-linux-ppc64-gnu': 1.10.1
      '@unrs/resolver-binding-linux-riscv64-gnu': 1.10.1
      '@unrs/resolver-binding-linux-riscv64-musl': 1.10.1
      '@unrs/resolver-binding-linux-s390x-gnu': 1.10.1
      '@unrs/resolver-binding-linux-x64-gnu': 1.10.1
      '@unrs/resolver-binding-linux-x64-musl': 1.10.1
      '@unrs/resolver-binding-wasm32-wasi': 1.10.1
      '@unrs/resolver-binding-win32-arm64-msvc': 1.10.1
      '@unrs/resolver-binding-win32-ia32-msvc': 1.10.1
      '@unrs/resolver-binding-win32-x64-msvc': 1.10.1
    dev: true

  /update-browserslist-db/1.1.3_browserslist@4.25.1:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1
    dev: true

  /uploadthing/7.7.3_next@14.2.23+tailwindcss@3.4.17:
    resolution: {integrity: sha512-jmPMVoqM+fWP1EXiwP6VBLTJykZ8z4ryrUr1N8yc87T+cxZw2vyvX2ReCaLhfjdbielPjHm486VCHtO2gYBFQA==}
    engines: {node: '>=18.13.0'}
    peerDependencies:
      express: '*'
      fastify: '*'
      h3: '*'
      next: '*'
      tailwindcss: ^3.0.0 || ^4.0.0-beta.0
    peerDependenciesMeta:
      express:
        optional: true
      fastify:
        optional: true
      h3:
        optional: true
      next:
        optional: true
      tailwindcss:
        optional: true
    dependencies:
      '@effect/platform': 0.85.2_effect@3.16.8
      '@standard-schema/spec': 1.0.0-beta.4
      '@uploadthing/mime-types': 0.3.5
      '@uploadthing/shared': 7.1.9
      effect: 3.16.8
      next: 14.2.23_react-dom@18.2.0+react@18.2.0
      tailwindcss: 3.4.17_ts-node@10.9.2
    dev: false

  /uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.1
    dev: true

  /use-callback-ref/1.3.3_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
      tslib: 2.8.1
    dev: false

  /use-composed-ref/1.4.0_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-djviaxuOOh7wkj0paeO1Q/4wMZ8Zrnag5H6yBvzN7AKKe8beOaED9SF5/ByLqsku8NP4zQqsvM2u3ew/tJK8/w==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /use-context-selector/1.4.4_react-dom@18.2.0+react@18.2.0:
    resolution: {integrity: sha512-pS790zwGxxe59GoBha3QYOwk8AFGp4DN6DOtH+eoqVmgBBRXVx4IlPDhJmmMiNQAgUaLlP+58aqRC3A4rdaSjg==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '*'
      react-native: '*'
      scheduler: '>=0.19.0'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    dev: false

  /use-deep-compare/1.3.0_react@18.2.0:
    resolution: {integrity: sha512-94iG+dEdEP/Sl3WWde+w9StIunlV8Dgj+vkt5wTwMoFQLaijiEZSXXy8KtcStpmEDtIptRJiNeD4ACTtVvnIKA==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      dequal: 2.0.3
      react: 18.2.0
    dev: false

  /use-file-picker/2.1.4_7060a42dad9f445ac5f4d9021dc0257f:
    resolution: {integrity: sha512-b4lZiAWrXi/QNUjTv0Q+S0hVcSFXIC9c4EUcrnYtdPtgK3T6xfi01YLVamhoY0k9WM9Cg4KyxD1TtM1e8dzQAQ==}
    engines: {node: '>=12'}
    peerDependencies:
      '@types/react': '>=16'
      '@types/react-dom': '>=16'
      react: '>=16'
    dependencies:
      '@types/react': 18.3.23
      '@types/react-dom': 18.3.7_@types+react@18.3.23
      file-selector: 2.1.2
      react: 18.2.0
    dev: false

  /use-isomorphic-layout-effect/1.2.1_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-tpZZ+EX0gaghDAiFR37hj5MgY6ZN55kLiPkJsKxBMZ6GZdOSPJXiOzPM984oPYZ5AnehYx5WQp1+ME8I/P/pRA==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
    dev: false

  /use-latest/1.3.0_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-mhg3xdm9NaM8q+gLT8KryJPnRFOz1/5XPBhmDEVZK1webPzDjrPk7f/mbpeLqTgB9msytYWANxgALOCJKnLvcQ==}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
      use-isomorphic-layout-effect: 1.2.1_888dbdc24f3e5b913723df3d4309886e
    dev: false

  /use-sidecar/1.1.3_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@types/react': 18.3.23
      detect-node-es: 1.1.0
      react: 18.2.0
      tslib: 2.8.1
    dev: false

  /use-sync-external-store/1.5.0_react@18.2.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}
    dev: true

  /utils-merge/1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}
    dev: false

  /uuid/10.0.0:
    resolution: {integrity: sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==}
    hasBin: true
    dev: false

  /uuid/11.1.0:
    resolution: {integrity: sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==}
    hasBin: true
    dev: false

  /v8-compile-cache-lib/3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==}
    dev: true

  /valid-data-url/3.0.1:
    resolution: {integrity: sha512-jOWVmzVceKlVVdwjNSenT4PbGghU0SBIizAev8ofZVgivk/TVHXSbNL8LP6M3spZvkR9/QolkyJavGSX5Cs0UA==}
    engines: {node: '>=10'}
    dev: false

  /validator/13.15.15:
    resolution: {integrity: sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A==}
    engines: {node: '>= 0.10'}
    dev: false

  /vary/1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}
    dev: false

  /vaul/0.9.9_813bbe29c70008681efc49db0ea78d97:
    resolution: {integrity: sha512-7afKg48srluhZwIkaU+lgGtFCUsYBSGOl8vcc8N/M3YQlZFlynHD15AE+pwrYdc826o7nrIND4lL9Y6b9WWZZQ==}
    peerDependencies:
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
    dependencies:
      '@radix-ui/react-dialog': 1.1.14_813bbe29c70008681efc49db0ea78d97
      react: 18.2.0
      react-dom: 18.2.0_react@18.2.0
    transitivePeerDependencies:
      - '@types/react'
      - '@types/react-dom'
    dev: false

  /vfile-message/4.0.2:
    resolution: {integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==}
    dependencies:
      '@types/unist': 3.0.3
      unist-util-stringify-position: 4.0.0
    dev: false

  /vfile/6.0.3:
    resolution: {integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==}
    dependencies:
      '@types/unist': 3.0.3
      vfile-message: 4.0.2
    dev: false

  /victory-vendor/36.9.2:
    resolution: {integrity: sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==}
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/d3-ease': 3.0.2
      '@types/d3-interpolate': 3.0.4
      '@types/d3-scale': 4.0.9
      '@types/d3-shape': 3.1.7
      '@types/d3-time': 3.0.4
      '@types/d3-timer': 3.0.2
      d3-array: 3.2.4
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-scale: 4.0.2
      d3-shape: 3.2.0
      d3-time: 3.1.0
      d3-timer: 3.0.1
    dev: false

  /w3c-keyname/2.2.8:
    resolution: {integrity: sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==}
    dev: false

  /watchpack/2.4.4:
    resolution: {integrity: sha512-c5EGNOiyxxV5qmTtAB7rbiXxi1ooX1pQKMLX/MIabJjRA0SJBQOjKF+KSVfHkr9U1cADPon0mRiVe/riyaiDUA==}
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
    dev: true

  /web-resource-inliner/6.0.1:
    resolution: {integrity: sha512-kfqDxt5dTB1JhqsCUQVFDj0rmY+4HLwGQIsLPbyrsN9y9WV/1oFDSx3BQ4GfCv9X+jVeQ7rouTqwK53rA/7t8A==}
    engines: {node: '>=10.0.0'}
    dependencies:
      ansi-colors: 4.1.3
      escape-goat: 3.0.0
      htmlparser2: 5.0.1
      mime: 2.6.0
      node-fetch: 2.7.0
      valid-data-url: 3.0.1
    transitivePeerDependencies:
      - encoding
    dev: false

  /web-streams-polyfill/4.0.0-beta.3:
    resolution: {integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==}
    engines: {node: '>= 14'}
    dev: false

  /webidl-conversions/3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}
    dev: false

  /webpack-sources/3.3.3:
    resolution: {integrity: sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==}
    engines: {node: '>=10.13.0'}
    dev: true

  /webpack/5.99.9:
    resolution: {integrity: sha512-brOPwM3JnmOa+7kd3NsmOUOwbDAj8FT9xDsG3IW0MgbN9yZV7Oi/s/+MNQ/EcSMqw7qfoRyXPoeEWT8zLVdVGg==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      browserslist: 4.25.1
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.2
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14_webpack@5.99.9
      watchpack: 2.4.4
      webpack-sources: 3.3.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: true

  /whatwg-url/5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1
    dev: false

  /which-boxed-primitive/1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1
    dev: true

  /which-builtin-type/1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19
    dev: true

  /which-collection/1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4
    dev: true

  /which-typed-array/1.1.19:
    resolution: {integrity: sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2
    dev: true

  /which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi/8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: true

  /wrappy/1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}
    dev: true

  /yaml/2.8.0:
    resolution: {integrity: sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==}
    engines: {node: '>= 14.6'}
    hasBin: true

  /yn/3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==}
    engines: {node: '>=6'}
    dev: true

  /yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: true

  /zod-to-json-schema/3.24.6_zod@3.25.70:
    resolution: {integrity: sha512-h/z3PKvcTcTetyjl1fkj79MHNEjm+HpD6NXheWjzOekY7kV+lwDYnHw+ivHkijnCSMz1yJaWBD9vu/Fcmk+vEg==}
    peerDependencies:
      zod: ^3.24.1
    dependencies:
      zod: 3.25.70
    dev: false

  /zod/3.25.70:
    resolution: {integrity: sha512-2WWzL2X2GpPU6as2xK1HFb6U5BZdJMdpB5Qtlan4a1KLYcZ6Gvox+mqZpxOd66sfe5AP3pEWpd2BC3f0yLH/nQ==}
    dev: false

  /zustand-x/3.0.4_99192c02dfac84441cb35d79f6436f58:
    resolution: {integrity: sha512-dVD8WUEpR/0mMdLah9j8i+r6PMAq9Ii2u+BX/9Bn4MHRt8sSnRQ90YMUlTVonZYAHGb2UHZwPpE2gMb8GtYDDw==}
    peerDependencies:
      zustand: '>=4.3.9'
    dependencies:
      immer: 10.1.1
      lodash.mapvalues: 4.6.0
      react-tracked: 1.7.14_react-dom@18.2.0+react@18.2.0
      zustand: 4.5.7_888dbdc24f3e5b913723df3d4309886e
    transitivePeerDependencies:
      - react
      - react-dom
      - react-native
      - scheduler
    dev: false

  /zustand/4.5.7_888dbdc24f3e5b913723df3d4309886e:
    resolution: {integrity: sha512-CHOUy7mu3lbD6o6LJLfllpjkzhHXSBlX8B9+qPddUsIfeF5S/UZ5q0kmCsnRqT1UHFQZchNFDDzMbQsuesHWlw==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0.6'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 18.3.23
      react: 18.2.0
      use-sync-external-store: 1.5.0_react@18.2.0
    dev: false

  /zwitch/2.0.4:
    resolution: {integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==}
    dev: false
