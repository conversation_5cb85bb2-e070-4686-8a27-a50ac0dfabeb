"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes/dist/types";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <Button
      variant="outline"
      className="flex w-full items-center justify-between gap-2 text-primary"
      onClick={toggleTheme}
    >
      <span>Change Theme</span>
      <div className="flex items-center">
        <Sun className="h-4 w-4 rotate-0 transition-all dark:hidden" />
        <Moon className="hidden h-4 w-4 rotate-0 transition-all dark:block" />
        <Switch
          checked={theme === "dark"}
          onCheckedChange={toggleTheme}
          className="ml-2"
        />
      </div>
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}
