#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT导出服务
使用python-pptx库将演示文稿数据导出为PowerPoint文件
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE
from pptx.dml.color import RGBColor
import uvicorn
import requests
from io import BytesIO
from PIL import Image
import base64
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="PPT导出服务", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型定义
class ExportRequest(BaseModel):
    """PPT导出请求模型"""
    title: str
    slides: Any  # 可以是PlateSlide[]数组或包含slides的对象

class PPTExporter:
    """PPT导出器类"""
    
    def __init__(self):
        self.presentation = None
        
    def create_presentation(self, title: str, slides_data: List[Dict[str, Any]]) -> bytes:
        """
        创建PowerPoint演示文稿
        
        Args:
            title: 演示文稿标题
            slides_data: 幻灯片数据列表
            
        Returns:
            bytes: PPT文件的二进制数据
        """
        logger.info(f"开始创建PPT演示文稿: {title}")
        
        # 创建新的演示文稿
        self.presentation = Presentation()
        
        # 添加标题页
        self._add_title_slide(title)
        
        # 添加内容页
        for i, slide_data in enumerate(slides_data):
            logger.info(f"处理第{i+1}张幻灯片")
            self._add_content_slide(slide_data)
        
        # 保存到内存
        from io import BytesIO
        ppt_stream = BytesIO()
        self.presentation.save(ppt_stream)
        ppt_stream.seek(0)
        
        logger.info(f"PPT创建完成，共{len(slides_data)+1}张幻灯片")
        return ppt_stream.getvalue()
    
    def _add_title_slide(self, title: str):
        """添加标题页"""
        slide_layout = self.presentation.slide_layouts[0]  # 标题页布局
        slide = self.presentation.slides.add_slide(slide_layout)
        
        # 设置标题
        title_shape = slide.shapes.title
        title_shape.text = title
        
        # 设置副标题
        subtitle_shape = slide.placeholders[1]
        subtitle_shape.text = f"生成时间: {datetime.now().strftime('%Y年%m月%d日')}"
    
    def _add_content_slide(self, slide_data: Dict[str, Any]):
        """添加内容页 - 重新设计以实现所见即所得效果"""
        # 使用空白布局以便完全自定义
        slide_layout = self.presentation.slide_layouts[6]  # 空白布局
        slide = self.presentation.slides.add_slide(slide_layout)

        # 设置白色背景
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = RGBColor(255, 255, 255)

        # 解析幻灯片内容
        content = slide_data.get('content', [])
        logger.info(f"处理幻灯片内容，包含 {len(content)} 个元素")

        # 检查是否有背景图片
        background_image = slide_data.get('backgroundImage')
        if background_image:
            self._add_background_image(slide, background_image)

        # 智能识别内容结构
        structured_items = self._parse_structured_content(content)

        if len(structured_items) >= 3:
            # 创建三栏布局（类似原图效果）
            self._create_three_column_layout(slide, structured_items[:3])
        else:
            # 回退到传统布局
            self._create_traditional_layout(slide, content)

    def _add_background_image(self, slide, image_url):
        """添加背景图片"""
        try:
            image_stream = None
            if image_url.startswith('data:image'):
                # 处理base64图片
                if ',' in image_url:
                    base64_data = image_url.split(',')[1]
                    image_data = base64.b64decode(base64_data)
                    image_stream = BytesIO(image_data)
            elif image_url.startswith('http'):
                # 下载网络图片
                response = requests.get(image_url, timeout=10)
                response.raise_for_status()
                image_stream = BytesIO(response.content)

            if image_stream:
                # 添加到右侧
                slide.shapes.add_picture(
                    image_stream,
                    Inches(6.5), Inches(0.5),
                    Inches(3.5), Inches(6.5)
                )
                logger.info("背景图片添加成功")
        except Exception as e:
            logger.error(f"添加背景图片失败: {e}")

    def _parse_structured_content(self, content):
        """解析结构化内容，识别三个主要部分"""
        structured_items = []

        for item in content:
            item_type = item.get('type', '')
            logger.info(f"解析内容类型: {item_type}")

            if item_type == 'visualization-list':
                # 使用现有的处理方法来提取数据
                children = item.get('children', [])
                logger.info(f"找到visualization-list，children: {len(children)} 个")

                for viz_item in children:
                    if viz_item.get('type') == 'visualization-item':
                        viz_children = viz_item.get('children', [])
                        viz_title = ""
                        viz_content = ""

                        for viz_child in viz_children:
                            if viz_child.get('type') in ['h3', 'h2', 'h1']:
                                viz_title = self._extract_text_from_children(viz_child.get('children', []))
                            elif viz_child.get('type') == 'p':
                                viz_content = self._extract_text_from_children(viz_child.get('children', []))

                        logger.info(f"提取到: 标题='{viz_title}', 内容='{viz_content}'")
                        if viz_title:
                            structured_items.append({
                                'title': viz_title,
                                'content': viz_content or ''
                            })

            elif item_type == 'column_group':
                # 处理列组
                children = item.get('children', [])
                logger.info(f"找到column_group，children: {len(children)} 个")

                for child in children:
                    if child.get('type') == 'column':
                        title = self._extract_text_from_children(child.get('children', []))
                        logger.info(f"提取到列标题: '{title}'")
                        if title:
                            structured_items.append({
                                'title': title,
                                'content': ''
                            })

        logger.info(f"解析完成，找到 {len(structured_items)} 个结构化项目")
        return structured_items

    def _create_three_column_layout(self, slide, items):
        """创建三栏布局"""
        # 左侧内容区域
        content_width = Inches(6)
        content_height = Inches(7)
        start_x = Inches(0.3)
        start_y = Inches(0.8)

        item_height = Inches(2)
        spacing = Inches(0.3)

        for i, item in enumerate(items):
            y_pos = start_y + i * (item_height + spacing)
            self._create_numbered_item(slide, i + 1, item['title'], item['content'],
                                     start_x, y_pos, content_width, item_height)

    def _create_numbered_item(self, slide, number, title, content, x, y, width, height):
        """创建带编号的项目"""
        try:
            # 创建编号文本框（模拟圆形效果）
            circle_size = Inches(0.4)
            circle_box = slide.shapes.add_textbox(
                x, y, circle_size, circle_size
            )

            # 设置编号文字
            circle_frame = circle_box.text_frame
            circle_frame.text = str(number)
            circle_para = circle_frame.paragraphs[0]
            circle_para.font.size = Pt(16)
            circle_para.font.bold = True
            circle_para.font.color.rgb = RGBColor(255, 255, 255)  # 白色
            circle_para.alignment = PP_ALIGN.CENTER

            # 设置文本框背景为紫色
            circle_fill = circle_box.fill
            circle_fill.solid()
            circle_fill.fore_color.rgb = RGBColor(102, 51, 153)  # 紫色

            # 创建标题文本框
            title_x = x + circle_size + Inches(0.2)
            title_width = width - circle_size - Inches(0.2)

            title_box = slide.shapes.add_textbox(
                title_x, y, title_width, Inches(0.5)
            )
            title_frame = title_box.text_frame
            title_frame.text = title
            title_para = title_frame.paragraphs[0]
            title_para.font.size = Pt(18)
            title_para.font.bold = True
            title_para.font.color.rgb = RGBColor(102, 51, 153)  # 紫色

            # 创建内容文本框
            if content:
                content_y = y + Inches(0.6)
                content_box = slide.shapes.add_textbox(
                    title_x, content_y, title_width, Inches(1.2)
                )
                content_frame = content_box.text_frame
                content_frame.text = content
                content_para = content_frame.paragraphs[0]
                content_para.font.size = Pt(12)
                content_para.font.color.rgb = RGBColor(64, 64, 64)  # 深灰色
                content_para.space_after = Pt(6)

        except Exception as e:
            logger.error(f"创建编号项目失败: {e}")

    def _create_traditional_layout(self, slide, content):
        """创建传统布局（回退方案）"""
        title_text = ""
        content_items = []

        for item in content:
            item_type = item.get('type', '')
            logger.info(f"处理内容项类型: {item_type}")

            # 提取标题
            if item_type in ['h1', 'h2', 'h3']:
                title_text = self._extract_text_from_children(item.get('children', []))
                if title_text:
                    logger.info(f"找到标题: {title_text}")

            # 处理各种内容类型
            elif item_type == 'p':
                text = self._extract_text_from_children(item.get('children', []))
                if text:
                    content_items.append(text)

            elif item_type == 'bullets':
                bullet_items = self._process_bullets(item)
                content_items.extend(bullet_items)

            elif item_type == 'ul':
                ul_items = self._process_ul(item)
                content_items.extend(ul_items)

        # 添加标题
        if title_text and title_text != '单击此处添加标题':
            title_box = slide.shapes.add_textbox(
                Inches(0.5), Inches(0.5), Inches(9), Inches(1)
            )
            title_frame = title_box.text_frame
            title_frame.text = title_text
            title_para = title_frame.paragraphs[0]
            title_para.font.size = Pt(28)
            title_para.font.bold = True
            title_para.alignment = PP_ALIGN.CENTER

        # 添加内容
        if content_items:
            content_box = slide.shapes.add_textbox(
                Inches(0.5), Inches(2), Inches(9), Inches(5)
            )
            content_frame = content_box.text_frame
            content_frame.clear()

            for i, item in enumerate(content_items):
                if i > 0:
                    content_frame.add_paragraph()

                para = content_frame.paragraphs[i]
                para.text = item
                para.font.size = Pt(16)
                para.space_after = Pt(6)

    def _extract_text_from_children(self, children: List[Dict[str, Any]]) -> str:
        """从children中提取文本内容"""
        for child in children:
            if child.get('text'):
                return child['text'].strip()
        return ""

    def _process_bullets(self, item: Dict[str, Any]) -> List[str]:
        """处理bullets类型内容"""
        result = []
        children = item.get('children', [])

        for bullet in children:
            if bullet.get('type') == 'bullet':
                bullet_children = bullet.get('children', [])
                bullet_title = ""
                bullet_content = ""

                for bullet_child in bullet_children:
                    if bullet_child.get('type') in ['h3', 'h2', 'h1']:
                        bullet_title = self._extract_text_from_children(bullet_child.get('children', []))
                    elif bullet_child.get('type') == 'p':
                        bullet_content = self._extract_text_from_children(bullet_child.get('children', []))

                if bullet_title:
                    result.append(f"• {bullet_title}")
                    if bullet_content:
                        result.append(f"  {bullet_content}")

        return result

    def _process_chart(self, item: Dict[str, Any]) -> List[str]:
        """处理chart类型内容"""
        result = []
        chart_data = item.get('data', [])
        chart_type = item.get('chartType', 'unknown')

        result.append(f"图表类型: {chart_type}")

        for data_item in chart_data:
            label = data_item.get('label', '')
            value = data_item.get('value', '')
            if label and value:
                result.append(f"• {label}: {value}")

        return result

    def _process_icons(self, item: Dict[str, Any]) -> List[str]:
        """处理icons类型内容"""
        result = []
        children = item.get('children', [])

        for icon_item in children:
            if icon_item.get('type') == 'icon-item':
                icon_children = icon_item.get('children', [])
                icon_title = ""
                icon_content = ""

                for icon_child in icon_children:
                    if icon_child.get('type') in ['h3', 'h2', 'h1']:
                        icon_title = self._extract_text_from_children(icon_child.get('children', []))
                    elif icon_child.get('type') == 'p':
                        icon_content = self._extract_text_from_children(icon_child.get('children', []))

                if icon_title:
                    result.append(f"• {icon_title}")
                    if icon_content:
                        result.append(f"  {icon_content}")

        return result

    def _process_columns(self, item: Dict[str, Any]) -> List[str]:
        """处理column_group类型内容"""
        result = []
        children = item.get('children', [])

        for column in children:
            if column.get('type') == 'column':
                column_children = column.get('children', [])
                column_title = ""
                column_content = ""

                for column_child in column_children:
                    if column_child.get('type') in ['h3', 'h2', 'h1']:
                        column_title = self._extract_text_from_children(column_child.get('children', []))
                    elif column_child.get('type') == 'p':
                        column_content = self._extract_text_from_children(column_child.get('children', []))

                if column_title:
                    result.append(f"• {column_title}")
                    if column_content:
                        result.append(f"  {column_content}")

        return result

    def _process_visualization_list(self, item: Dict[str, Any]) -> List[str]:
        """处理visualization-list类型内容"""
        result = []
        children = item.get('children', [])

        for viz_item in children:
            if viz_item.get('type') == 'visualization-item':
                viz_children = viz_item.get('children', [])
                viz_title = ""
                viz_content = ""

                for viz_child in viz_children:
                    if viz_child.get('type') in ['h3', 'h2', 'h1']:
                        viz_title = self._extract_text_from_children(viz_child.get('children', []))
                    elif viz_child.get('type') == 'p':
                        viz_content = self._extract_text_from_children(viz_child.get('children', []))

                if viz_title:
                    result.append(f"• {viz_title}")
                    if viz_content:
                        result.append(f"  {viz_content}")

        return result

    def _process_cycle(self, item: Dict[str, Any]) -> List[str]:
        """处理cycle类型内容"""
        result = []
        children = item.get('children', [])

        for cycle_item in children:
            if cycle_item.get('type') == 'cycle-item':
                cycle_children = cycle_item.get('children', [])
                cycle_title = ""
                cycle_content = ""

                for cycle_child in cycle_children:
                    if cycle_child.get('type') in ['h3', 'h2', 'h1']:
                        cycle_title = self._extract_text_from_children(cycle_child.get('children', []))
                    elif cycle_child.get('type') == 'p':
                        cycle_content = self._extract_text_from_children(cycle_child.get('children', []))

                if cycle_title:
                    result.append(f"• {cycle_title}")
                    if cycle_content:
                        result.append(f"  {cycle_content}")

        return result

    def _process_staircase(self, item: Dict[str, Any]) -> List[str]:
        """处理staircase类型内容"""
        result = []
        children = item.get('children', [])

        for stair_item in children:
            if stair_item.get('type') == 'stair-item':
                stair_children = stair_item.get('children', [])
                stair_title = ""
                stair_content = ""

                for stair_child in stair_children:
                    if stair_child.get('type') in ['h3', 'h2', 'h1']:
                        stair_title = self._extract_text_from_children(stair_child.get('children', []))
                    elif stair_child.get('type') == 'p':
                        stair_content = self._extract_text_from_children(stair_child.get('children', []))

                if stair_title:
                    result.append(f"• {stair_title}")
                    if stair_content:
                        result.append(f"  {stair_content}")

        return result

    def _process_ul(self, item: Dict[str, Any]) -> List[str]:
        """处理ul类型内容（传统无序列表）"""
        result = []
        children = item.get('children', [])

        for li in children:
            if li.get('type') == 'li':
                li_children = li.get('children', [])
                if li_children and li_children[0].get('children'):
                    text = li_children[0]['children'][0].get('text', '').strip()
                    if text:
                        result.append(f"• {text}")

        return result

@app.get("/")
async def root():
    """健康检查接口"""
    return {"message": "PPT导出服务运行正常", "timestamp": datetime.now().isoformat()}

@app.post("/export")
async def export_ppt(request_data: ExportRequest):
    """
    导出PPT接口

    Args:
        request_data: 包含演示文稿数据的请求体

    Returns:
        PPT文件的二进制响应
    """
    try:
        logger.info("收到PPT导出请求")
        logger.info(f"请求数据类型: {type(request_data.slides)}")

        # 解析请求数据
        title = request_data.title or '未命名演示文稿'

        # 处理slides数据 - 可能是数组或包含slides的对象
        slides_data = request_data.slides
        if isinstance(slides_data, dict) and 'slides' in slides_data:
            # 如果是 {slides: [PlateSlide[]]} 格式
            slides_data = slides_data['slides']
        elif not isinstance(slides_data, list):
            # 如果不是列表，尝试转换
            slides_data = []

        logger.info(f"处理后的幻灯片数量: {len(slides_data) if isinstance(slides_data, list) else 0}")

        if not slides_data:
            raise HTTPException(status_code=400, detail="幻灯片数据不能为空")
        
        # 创建PPT导出器
        exporter = PPTExporter()
        
        # 生成PPT文件
        ppt_bytes = exporter.create_presentation(title, slides_data)
        
        # 返回文件响应
        import urllib.parse
        filename = f"{title}.pptx"
        # 对文件名进行URL编码以支持中文
        encoded_filename = urllib.parse.quote(filename, safe='')
        return Response(
            content=ppt_bytes,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}",
                "Content-Length": str(len(ppt_bytes))
            }
        )
        
    except Exception as e:
        logger.error(f"导出PPT时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

if __name__ == "__main__":
    # 启动服务
    port = int(os.getenv("PORT", 8001))
    logger.info(f"启动PPT导出服务，端口: {port}")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
