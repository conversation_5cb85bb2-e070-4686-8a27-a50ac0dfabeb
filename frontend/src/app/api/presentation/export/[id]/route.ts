import { db } from "@/server/db";
import { NextRequest, NextResponse } from "next/server";

// PPT导出服务的URL
const PPT_EXPORT_SERVICE_URL = process.env.PPT_EXPORT_SERVICE_URL || "http://localhost:8001";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: "演示文稿ID不能为空" },
        { status: 400 }
      );
    }

    // 从数据库获取演示文稿数据
    const presentation = await db.baseDocument.findUnique({
      where: { id },
      include: {
        presentation: {
          select: {
            id: true,
            content: true,
            theme: true,
            outline: true,
          },
        },
      },
    });

    if (!presentation || !presentation.presentation) {
      return NextResponse.json(
        { error: "演示文稿不存在" },
        { status: 404 }
      );
    }

    // 准备导出数据
    const presentationContent = presentation.presentation.content as any;
    let slidesData = [];

    // 处理content数据结构
    if (presentationContent && typeof presentationContent === 'object') {
      if (Array.isArray(presentationContent)) {
        // 如果content直接是数组
        slidesData = presentationContent;
      } else if (presentationContent.slides && Array.isArray(presentationContent.slides)) {
        // 如果content是包含slides的对象
        slidesData = presentationContent.slides;
      }
    }

    const exportData = {
      title: presentation.title,
      slides: slidesData,
    };

    console.log("准备导出PPT数据:", {
      title: exportData.title,
      slidesCount: Array.isArray(exportData.slides) ? exportData.slides.length : 0,
      contentType: typeof presentationContent,
      contentStructure: presentationContent ? Object.keys(presentationContent) : 'null'
    });

    // 调用PPT导出服务
    const exportResponse = await fetch(`${PPT_EXPORT_SERVICE_URL}/export`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(exportData),
    });

    if (!exportResponse.ok) {
      const errorText = await exportResponse.text();
      console.error("PPT导出服务错误:", errorText);
      return NextResponse.json(
        { error: "PPT导出服务错误" },
        { status: 500 }
      );
    }

    // 获取PPT文件数据
    const pptBuffer = await exportResponse.arrayBuffer();
    
    // 返回PPT文件
    return new NextResponse(pptBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "Content-Disposition": `attachment; filename="${encodeURIComponent(presentation.title)}.pptx"`,
        "Content-Length": pptBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    console.error("导出PPT时发生错误:", error);
    return NextResponse.json(
      { error: "导出失败，请稍后重试" },
      { status: 500 }
    );
  }
}
