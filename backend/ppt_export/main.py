#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT导出服务
使用python-pptx库将演示文稿数据导出为PowerPoint文件
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from fastapi import FastAPI, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="PPT导出服务", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型定义
class ExportRequest(BaseModel):
    """PPT导出请求模型"""
    title: str
    slides: Any  # 可以是PlateSlide[]数组或包含slides的对象

class PPTExporter:
    """PPT导出器类"""
    
    def __init__(self):
        self.presentation = None
        
    def create_presentation(self, title: str, slides_data: List[Dict[str, Any]]) -> bytes:
        """
        创建PowerPoint演示文稿
        
        Args:
            title: 演示文稿标题
            slides_data: 幻灯片数据列表
            
        Returns:
            bytes: PPT文件的二进制数据
        """
        logger.info(f"开始创建PPT演示文稿: {title}")
        
        # 创建新的演示文稿
        self.presentation = Presentation()
        
        # 添加标题页
        self._add_title_slide(title)
        
        # 添加内容页
        for i, slide_data in enumerate(slides_data):
            logger.info(f"处理第{i+1}张幻灯片")
            self._add_content_slide(slide_data)
        
        # 保存到内存
        from io import BytesIO
        ppt_stream = BytesIO()
        self.presentation.save(ppt_stream)
        ppt_stream.seek(0)
        
        logger.info(f"PPT创建完成，共{len(slides_data)+1}张幻灯片")
        return ppt_stream.getvalue()
    
    def _add_title_slide(self, title: str):
        """添加标题页"""
        slide_layout = self.presentation.slide_layouts[0]  # 标题页布局
        slide = self.presentation.slides.add_slide(slide_layout)
        
        # 设置标题
        title_shape = slide.shapes.title
        title_shape.text = title
        
        # 设置副标题
        subtitle_shape = slide.placeholders[1]
        subtitle_shape.text = f"生成时间: {datetime.now().strftime('%Y年%m月%d日')}"
    
    def _add_content_slide(self, slide_data: Dict[str, Any]):
        """添加内容页"""
        # 使用标题和内容布局
        slide_layout = self.presentation.slide_layouts[1]
        slide = self.presentation.slides.add_slide(slide_layout)

        # 解析幻灯片内容
        content = slide_data.get('content', [])

        # 提取标题和内容
        title_text = ""
        content_items = []

        logger.info(f"处理幻灯片内容，包含 {len(content)} 个元素")

        for item in content:
            item_type = item.get('type', '')
            logger.info(f"处理内容项类型: {item_type}")

            # 提取标题
            if item_type in ['h1', 'h2', 'h3']:
                title_text = self._extract_text_from_children(item.get('children', []))
                if title_text:
                    logger.info(f"找到标题: {title_text}")

            # 处理各种内容类型
            elif item_type == 'p':
                text = self._extract_text_from_children(item.get('children', []))
                if text:
                    content_items.append(text)

            elif item_type == 'bullets':
                bullet_items = self._process_bullets(item)
                content_items.extend(bullet_items)

            elif item_type == 'chart':
                chart_content = self._process_chart(item)
                if chart_content:
                    content_items.extend(chart_content)

            elif item_type == 'icons':
                icon_items = self._process_icons(item)
                content_items.extend(icon_items)

            elif item_type == 'column_group':
                column_items = self._process_columns(item)
                content_items.extend(column_items)

            elif item_type == 'visualization-list':
                viz_items = self._process_visualization_list(item)
                content_items.extend(viz_items)

            elif item_type == 'cycle':
                cycle_items = self._process_cycle(item)
                content_items.extend(cycle_items)

            elif item_type == 'staircase':
                stair_items = self._process_staircase(item)
                content_items.extend(stair_items)

            elif item_type == 'ul':
                ul_items = self._process_ul(item)
                content_items.extend(ul_items)

        # 设置标题
        if title_text:
            title_shape = slide.shapes.title
            title_shape.text = title_text

            # 设置标题样式
            title_paragraph = title_shape.text_frame.paragraphs[0]
            title_paragraph.font.size = Pt(32)
            title_paragraph.font.bold = True
            logger.info(f"设置幻灯片标题: {title_text}")

        # 设置内容
        if content_items:
            content_shape = slide.placeholders[1]
            content_text = '\n'.join(content_items)
            content_shape.text = content_text

            # 设置内容样式
            for paragraph in content_shape.text_frame.paragraphs:
                paragraph.font.size = Pt(18)
                paragraph.space_after = Pt(12)

            logger.info(f"设置幻灯片内容，共 {len(content_items)} 项")
        else:
            logger.warning("幻灯片没有找到可显示的内容")

    def _extract_text_from_children(self, children: List[Dict[str, Any]]) -> str:
        """从children中提取文本内容"""
        for child in children:
            if child.get('text'):
                return child['text'].strip()
        return ""

    def _process_bullets(self, item: Dict[str, Any]) -> List[str]:
        """处理bullets类型内容"""
        result = []
        children = item.get('children', [])

        for bullet in children:
            if bullet.get('type') == 'bullet':
                bullet_children = bullet.get('children', [])
                bullet_title = ""
                bullet_content = ""

                for bullet_child in bullet_children:
                    if bullet_child.get('type') in ['h3', 'h2', 'h1']:
                        bullet_title = self._extract_text_from_children(bullet_child.get('children', []))
                    elif bullet_child.get('type') == 'p':
                        bullet_content = self._extract_text_from_children(bullet_child.get('children', []))

                if bullet_title:
                    result.append(f"• {bullet_title}")
                    if bullet_content:
                        result.append(f"  {bullet_content}")

        return result

    def _process_chart(self, item: Dict[str, Any]) -> List[str]:
        """处理chart类型内容"""
        result = []
        chart_data = item.get('data', [])
        chart_type = item.get('chartType', 'unknown')

        result.append(f"图表类型: {chart_type}")

        for data_item in chart_data:
            label = data_item.get('label', '')
            value = data_item.get('value', '')
            if label and value:
                result.append(f"• {label}: {value}")

        return result

    def _process_icons(self, item: Dict[str, Any]) -> List[str]:
        """处理icons类型内容"""
        result = []
        children = item.get('children', [])

        for icon_item in children:
            if icon_item.get('type') == 'icon-item':
                icon_children = icon_item.get('children', [])
                icon_title = ""
                icon_content = ""

                for icon_child in icon_children:
                    if icon_child.get('type') in ['h3', 'h2', 'h1']:
                        icon_title = self._extract_text_from_children(icon_child.get('children', []))
                    elif icon_child.get('type') == 'p':
                        icon_content = self._extract_text_from_children(icon_child.get('children', []))

                if icon_title:
                    result.append(f"• {icon_title}")
                    if icon_content:
                        result.append(f"  {icon_content}")

        return result

    def _process_columns(self, item: Dict[str, Any]) -> List[str]:
        """处理column_group类型内容"""
        result = []
        children = item.get('children', [])

        for column in children:
            if column.get('type') == 'column':
                column_children = column.get('children', [])
                column_title = ""
                column_content = ""

                for column_child in column_children:
                    if column_child.get('type') in ['h3', 'h2', 'h1']:
                        column_title = self._extract_text_from_children(column_child.get('children', []))
                    elif column_child.get('type') == 'p':
                        column_content = self._extract_text_from_children(column_child.get('children', []))

                if column_title:
                    result.append(f"• {column_title}")
                    if column_content:
                        result.append(f"  {column_content}")

        return result

    def _process_visualization_list(self, item: Dict[str, Any]) -> List[str]:
        """处理visualization-list类型内容"""
        result = []
        children = item.get('children', [])

        for viz_item in children:
            if viz_item.get('type') == 'visualization-item':
                viz_children = viz_item.get('children', [])
                viz_title = ""
                viz_content = ""

                for viz_child in viz_children:
                    if viz_child.get('type') in ['h3', 'h2', 'h1']:
                        viz_title = self._extract_text_from_children(viz_child.get('children', []))
                    elif viz_child.get('type') == 'p':
                        viz_content = self._extract_text_from_children(viz_child.get('children', []))

                if viz_title:
                    result.append(f"• {viz_title}")
                    if viz_content:
                        result.append(f"  {viz_content}")

        return result

    def _process_cycle(self, item: Dict[str, Any]) -> List[str]:
        """处理cycle类型内容"""
        result = []
        children = item.get('children', [])

        for cycle_item in children:
            if cycle_item.get('type') == 'cycle-item':
                cycle_children = cycle_item.get('children', [])
                cycle_title = ""
                cycle_content = ""

                for cycle_child in cycle_children:
                    if cycle_child.get('type') in ['h3', 'h2', 'h1']:
                        cycle_title = self._extract_text_from_children(cycle_child.get('children', []))
                    elif cycle_child.get('type') == 'p':
                        cycle_content = self._extract_text_from_children(cycle_child.get('children', []))

                if cycle_title:
                    result.append(f"• {cycle_title}")
                    if cycle_content:
                        result.append(f"  {cycle_content}")

        return result

    def _process_staircase(self, item: Dict[str, Any]) -> List[str]:
        """处理staircase类型内容"""
        result = []
        children = item.get('children', [])

        for stair_item in children:
            if stair_item.get('type') == 'stair-item':
                stair_children = stair_item.get('children', [])
                stair_title = ""
                stair_content = ""

                for stair_child in stair_children:
                    if stair_child.get('type') in ['h3', 'h2', 'h1']:
                        stair_title = self._extract_text_from_children(stair_child.get('children', []))
                    elif stair_child.get('type') == 'p':
                        stair_content = self._extract_text_from_children(stair_child.get('children', []))

                if stair_title:
                    result.append(f"• {stair_title}")
                    if stair_content:
                        result.append(f"  {stair_content}")

        return result

    def _process_ul(self, item: Dict[str, Any]) -> List[str]:
        """处理ul类型内容（传统无序列表）"""
        result = []
        children = item.get('children', [])

        for li in children:
            if li.get('type') == 'li':
                li_children = li.get('children', [])
                if li_children and li_children[0].get('children'):
                    text = li_children[0]['children'][0].get('text', '').strip()
                    if text:
                        result.append(f"• {text}")

        return result

@app.get("/")
async def root():
    """健康检查接口"""
    return {"message": "PPT导出服务运行正常", "timestamp": datetime.now().isoformat()}

@app.post("/export")
async def export_ppt(request_data: ExportRequest):
    """
    导出PPT接口

    Args:
        request_data: 包含演示文稿数据的请求体

    Returns:
        PPT文件的二进制响应
    """
    try:
        logger.info("收到PPT导出请求")
        logger.info(f"请求数据类型: {type(request_data.slides)}")

        # 解析请求数据
        title = request_data.title or '未命名演示文稿'

        # 处理slides数据 - 可能是数组或包含slides的对象
        slides_data = request_data.slides
        if isinstance(slides_data, dict) and 'slides' in slides_data:
            # 如果是 {slides: [PlateSlide[]]} 格式
            slides_data = slides_data['slides']
        elif not isinstance(slides_data, list):
            # 如果不是列表，尝试转换
            slides_data = []

        logger.info(f"处理后的幻灯片数量: {len(slides_data) if isinstance(slides_data, list) else 0}")

        if not slides_data:
            raise HTTPException(status_code=400, detail="幻灯片数据不能为空")
        
        # 创建PPT导出器
        exporter = PPTExporter()
        
        # 生成PPT文件
        ppt_bytes = exporter.create_presentation(title, slides_data)
        
        # 返回文件响应
        import urllib.parse
        filename = f"{title}.pptx"
        # 对文件名进行URL编码以支持中文
        encoded_filename = urllib.parse.quote(filename, safe='')
        return Response(
            content=ppt_bytes,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}",
                "Content-Length": str(len(ppt_bytes))
            }
        )
        
    except Exception as e:
        logger.error(f"导出PPT时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

if __name__ == "__main__":
    # 启动服务
    port = int(os.getenv("PORT", 8001))
    logger.info(f"启动PPT导出服务，端口: {port}")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
