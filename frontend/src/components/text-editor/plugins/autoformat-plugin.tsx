"use client";

import type { AutoformatRule } from "@udecode/plate-autoformat";
import type { SlateEditor } from "@udecode/plate-common";

import {
  autoformatArrow,
  autoformatLegal,
  autoformatLegalHtml,
  autoformatMath,
  autoformatPunctuation,
  autoformatSmartQuotes,
} from "@udecode/plate-autoformat";
import { AutoformatPlugin } from "@udecode/plate-autoformat/react";
import {
  BoldPlugin,
  CodePlugin,
  ItalicPlugin,
  StrikethroughPlugin,
  SubscriptPlugin,
  SuperscriptPlugin,
  UnderlinePlugin,
} from "@udecode/plate-basic-marks/react";
import { BlockquotePlugin } from "@udecode/plate-block-quote/react";
import { insertEmptyCodeBlock } from "@udecode/plate-code-block";
import {
  CodeBlockPlugin,
  CodeLinePlugin,
} from "@udecode/plate-code-block/react";
import {
  getParentNode,
  insertNodes,
  isElement,
  isType,
  setNodes,
} from "@udecode/plate-common";
import { ParagraphPlugin } from "@udecode/plate-common/react";
import { HEADING_KEYS } from "@udecode/plate-heading";
import { HighlightPlugin } from "@udecode/plate-highlight/react";
import { HorizontalRulePlugin } from "@udecode/plate-horizontal-rule/react";
import {
  INDENT_LIST_KEYS,
  ListStyleType,
  toggleIndentList,
} from "@udecode/plate-indent-list";
import { TogglePlugin, openNextToggles } from "@udecode/plate-toggle/react";

export const format = (editor: SlateEditor, customFormatting) => {
  if (editor.selection) {
    const parentEntry = getParentNode(editor, editor.selection);

    if (!parentEntry) return;

    const [node] = parentEntry;

    if (
      isElement(node) &&
      !isType(editor, node, CodeBlockPlugin.key) &&
      !isType(editor, node, CodeLinePlugin.key)
    ) {
      customFormatting();
    }
  }
};

export const autoformatMarks: AutoformatRule[] = [
  {
    match: "***",
    mode: "mark",
    type: [BoldPlugin.key, ItalicPlugin.key],
  },
  {
    match: "__*",
    mode: "mark",
    type: [UnderlinePlugin.key, ItalicPlugin.key],
  },
  {
    match: "__**",
    mode: "mark",
    type: [UnderlinePlugin.key, BoldPlugin.key],
  },
  {
    match: "___***",
    mode: "mark",
    type: [UnderlinePlugin.key, BoldPlugin.key, ItalicPlugin.key],
  },
  {
    match: "**",
    mode: "mark",
    type: BoldPlugin.key,
  },
  {
    match: "__",
    mode: "mark",
    type: UnderlinePlugin.key,
  },
  {
    match: "*",
    mode: "mark",
    type: ItalicPlugin.key,
  },
  {
    match: "_",
    mode: "mark",
    type: ItalicPlugin.key,
  },
  {
    match: "~~",
    mode: "mark",
    type: StrikethroughPlugin.key,
  },
  {
    match: "^",
    mode: "mark",
    type: SuperscriptPlugin.key,
  },
  {
    match: "~",
    mode: "mark",
    type: SubscriptPlugin.key,
  },
  {
    match: "==",
    mode: "mark",
    type: HighlightPlugin.key,
  },
  {
    match: "≡",
    mode: "mark",
    type: HighlightPlugin.key,
  },
  {
    match: "`",
    mode: "mark",
    type: CodePlugin.key,
  },
];

export const autoformatBlocks: AutoformatRule[] = [
  {
    match: "# ",
    mode: "block",
    type: HEADING_KEYS.h1,
  },
  {
    match: "## ",
    mode: "block",
    type: HEADING_KEYS.h2,
  },
  {
    match: "### ",
    mode: "block",
    type: HEADING_KEYS.h3,
  },
  {
    match: "#### ",
    mode: "block",
    type: HEADING_KEYS.h4,
  },
  {
    match: "##### ",
    mode: "block",
    type: HEADING_KEYS.h5,
  },
  {
    match: "###### ",
    mode: "block",
    type: HEADING_KEYS.h6,
  },
  {
    match: "> ",
    mode: "block",
    type: BlockquotePlugin.key,
  },
  {
    format: (editor) => {
      insertEmptyCodeBlock(editor, {
        defaultType: ParagraphPlugin.key,
        insertNodesOptions: { select: true },
      });
    },
    match: "```",
    mode: "block",
    triggerAtBlockStart: false,
    type: CodeBlockPlugin.key,
  },
  {
    match: "+ ",
    mode: "block",
    preFormat: openNextToggles,
    type: TogglePlugin.key,
  },
  {
    format: (editor) => {
      setNodes(editor, { type: HorizontalRulePlugin.key });
      insertNodes(editor, {
        children: [{ text: "" }],
        type: ParagraphPlugin.key,
      });
    },
    match: ["---", "—-", "___ "],
    mode: "block",
    type: HorizontalRulePlugin.key,
  },
];

export const autoformatIndentLists: AutoformatRule[] = [
  {
    format: (editor) => {
      toggleIndentList(editor, {
        listStyleType: ListStyleType.Disc,
      });
    },
    match: ["* ", "- "],
    mode: "block",
    type: "list",
  },
  {
    format: (editor) =>
      toggleIndentList(editor, {
        listStyleType: ListStyleType.Decimal,
      }),
    match: [String.raw`^\d+\.$ `, String.raw`^\d+\)$ `],
    matchByRegex: true,
    mode: "block",
    type: "list",
  },
  {
    format: (editor) => {
      toggleIndentList(editor, {
        listStyleType: INDENT_LIST_KEYS.todo,
      });
      setNodes(editor, {
        checked: false,
        listStyleType: INDENT_LIST_KEYS.todo,
      });
    },
    match: ["[] ", "[ ] "],
    mode: "block",
    type: "list",
  },

  {
    format: (editor) => {
      toggleIndentList(editor, {
        listStyleType: INDENT_LIST_KEYS.todo,
      });
      setNodes(editor, {
        checked: true,
        listStyleType: INDENT_LIST_KEYS.todo,
      });
    },
    match: ["[x] ", "[X] "],
    mode: "block",
    type: "list",
  },
];

export const autoformatPlugin = AutoformatPlugin.configure({
  options: {
    enableUndoOnDelete: true,
    rules: [
      ...autoformatBlocks,
      ...autoformatMarks,
      ...autoformatSmartQuotes,
      ...autoformatPunctuation,
      ...autoformatLegal,
      ...autoformatLegalHtml,
      ...autoformatArrow,
      ...autoformatMath,
      ...autoformatIndentLists,
    ],
  },
});
